<?php

use App\Http\Requests\StoreSupplyBoxMovingRequest;
use App\Models\Employee;
use App\Models\Supply;
use App\Models\SupplyBox;
use App\Models\SupplyBoxMoving;
use App\Models\SupplyLocation;
use App\Models\SupplyLocationQuantity;
use App\Models\Warehouse;
use App\Repositories\SupplyBoxMovingRepository;
use App\Repositories\SupplyLocationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\JsonResponse;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->repository = new SupplyBoxMovingRepository(app()->make(SupplyLocationRepository::class));
    $this->warehouse = Warehouse::factory()->create();
    $this->supply = Supply::factory()->create();
    $this->employee = Employee::factory()->create();
    // $this->supplyLocation = SupplyLocation::factory()->create(
    //     [
    //         'warehouse_id' => $this->warehouse->id,
    //         'barcode' => 'SUPPLYLOCATION',
    //     ]
    // );
    // $this->supplyBox = SupplyBox::factory()->create(
    //     [
    //         'warehouse_id' => $this->warehouse->id,
    //         'supply_id' => $this->supply->id,
    //         'quantity' => 10,
    //         'barcode' => 'SUPPLYBOX',
    //         'location_id' => $this->supplyLocation->id,
    //     ]
    // );
});

it('fetchAll returns paginated data structure', function () {
    $input = ['limit' => 5, 'page' => 1, 'warehouse_id' => $this->warehouse->id];
    $result = $this->repository->fetchAll($input);

    expect($result)->toBeArray()
        ->and($result)->toHaveKeys(['total', 'data'])
        ->and($result['data'])->toBeCollection();
});

it('create returns JsonResponse on success', function () {
    $location = SupplyLocation::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'SL' . time(),
    ]);
    $locationQuantity = SupplyLocationQuantity::factory()->create([
        'location_id' => $location->id,
        'supply_id' => $this->supply->id,
        'quantity' => 99,
    ]);

    $newLocation = SupplyLocation::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'NSL' . time(),
    ]);
    $newLocationQuantity = SupplyLocationQuantity::factory()->create([
        'location_id' => $newLocation->id,
        'supply_id' => $this->supply->id,
        'quantity' => 0,
    ]);

    $supplyBox = SupplyBox::factory()->create([
        'location_id' => $location->id,
        'supply_id' => $this->supply->id,
        'quantity' => 11,
        'barcode' => 'SB' . time(),
    ]);

    $request = Mockery::mock(StoreSupplyBoxMovingRequest::class);
    $request->shouldReceive('getValidatedBox')->andReturn($supplyBox);
    $request->shouldReceive('getValidatedWarehouseId')->andReturn($this->warehouse->id);
    $request->shouldReceive('get')->with('supply_location_barcode')->andReturn($newLocation->barcode);
    $request->shouldReceive('get')->with('employee_id')->andReturn($this->employee->code);
    $request->shouldReceive('get')->with('id_time_checking')->andReturn(null);

    $response = $this->repository->create($request);
    $locationQuantityAfter = SupplyLocationQuantity::where('location_id', $location->id)
        ->where('supply_id', $this->supply->id)
        ->first();
    $newLocationQuantityAfter = SupplyLocationQuantity::where('location_id', $newLocation->id)
        ->where('supply_id', $this->supply->id)
        ->first();

    expect($locationQuantityAfter->quantity)->toBe($locationQuantity->quantity - $supplyBox->quantity);
    expect($newLocationQuantityAfter->quantity)->toBe($newLocationQuantity->quantity + $supplyBox->quantity);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->getStatusCode())->toBe(201);
});

it('create returns error JsonResponse if new location matches old location', function () {
    $location = SupplyLocation::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'SUPPLYLOCATION123',
    ]);

    $supplyBox = SupplyBox::factory()->create([
        'location_id' => $location->id,
        'supply_id' => $this->supply->id,
        'quantity' => 10,
        'barcode' => 'SUPPLYBOX123',
    ]);

    $request = Mockery::mock(StoreSupplyBoxMovingRequest::class);
    $request->shouldReceive('getValidatedBox')->andReturn($supplyBox);
    $request->shouldReceive('getValidatedWarehouseId')->andReturn(1);
    $request->shouldReceive('get')->with('supply_location_barcode')->andReturn($location->barcode);
    $request->shouldReceive('get')->with('employee_id')->andReturn($this->employee->code);
    $request->shouldReceive('get')->with('id_time_checking')->andReturn(null);

    $response = $this->repository->create($request);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->getStatusCode())->toBe(422);
});

it('create returns error JsonResponse if new location invalid', function () {
    $location = SupplyLocation::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'SUPPLYLOCATION123',
    ]);

    $supplyBox = SupplyBox::factory()->create([
        'location_id' => $location->id,
        'supply_id' => $this->supply->id,
        'quantity' => 10,
        'barcode' => 'SUPPLYBOX123',
    ]);

    $request = Mockery::mock(StoreSupplyBoxMovingRequest::class);
    $request->shouldReceive('getValidatedBox')->andReturn($supplyBox);
    $request->shouldReceive('getValidatedWarehouseId')->andReturn(1);
    $request->shouldReceive('get')->with('supply_location_barcode')->andReturn('xxxxx');
    $request->shouldReceive('get')->with('employee_id')->andReturn($this->employee->code);
    $request->shouldReceive('get')->with('id_time_checking')->andReturn(null);

    $response = $this->repository->create($request);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->getStatusCode())->toBe(422);
});

it('revert returns error if not latest moving', function () {
    $params = ['id' => 999999];

    try {
        $response = $this->repository->revert($params);
        expect($response)->toBeInstanceOf(JsonResponse::class);
        expect($response->getStatusCode())->toBe(422);
    } catch (\Exception $e) {
        expect(true)->toBeTrue();
    }
});

it('revert returns error JsonResponse if not latest moving', function () {
    $moving = SupplyBoxMoving::factory()->create();
    $latestMoving = SupplyBoxMoving::factory()->create(['supply_box_id' => $moving->supply_box_id]);

    $this->assertTrue($latestMoving->id > $moving->id);

    $params = ['id' => $moving->id];

    $response = $this->repository->revert($params);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->getStatusCode())->toBe(422);
    expect($response->getData()->message)->toContain('Cannot revert box moving, it is not the latest moving');
});

it('revert successfully reverts latest moving', function () {
    $location = SupplyLocation::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'SL' . time(),
    ]);
    $locationQuantity = SupplyLocationQuantity::factory()->create([
        'location_id' => $location->id,
        'supply_id' => $this->supply->id,
        'quantity' => 88,
    ]);

    $newLocation = SupplyLocation::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'NSL' . time(),
    ]);
    $newLocationQuantity = SupplyLocationQuantity::factory()->create([
        'location_id' => $newLocation->id,
        'supply_id' => $this->supply->id,
        'quantity' => 11,
    ]);

    $supplyBox = SupplyBox::factory()->create([
        'location_id' => $location->id,
        'supply_id' => $this->supply->id,
        'quantity' => 11,
        'barcode' => 'SB' . time(),
    ]);

    $moving = SupplyBoxMoving::factory()->create([
        'supply_box_id' => $supplyBox->id,
        'supply_id' => $this->supply->id,
        'quantity' => $supplyBox->quantity,
        'pre_supply_location_id' => $location->id,
        'supply_location_id' => $newLocation->id,
    ]);

    $params = ['id' => $moving->id];

    $response = $this->repository->revert($params);
    $locationQuantityAfter = SupplyLocationQuantity::where('location_id', $location->id)
        ->where('supply_id', $this->supply->id)
        ->first();
    $newLocationQuantityAfter = SupplyLocationQuantity::where('location_id', $newLocation->id)
        ->where('supply_id', $this->supply->id)
        ->first();

    expect($locationQuantityAfter->quantity)->toBe($locationQuantity->quantity + $supplyBox->quantity);
    expect($newLocationQuantityAfter->quantity)->toBe($newLocationQuantity->quantity - $supplyBox->quantity);
    expect(SupplyBoxMoving::find($moving->id))->toBeNull();

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect(in_array($response->getStatusCode(), [200, 201]))->toBeTrue();
});
