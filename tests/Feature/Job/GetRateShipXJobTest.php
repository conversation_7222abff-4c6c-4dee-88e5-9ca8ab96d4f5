<?php

use App\Exceptions\ShipXException;
use App\Jobs\GetRateShipXJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\Store;
use App\Models\StoreSetting;
use App\Models\Warehouse;
use App\Repositories\LabelRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test data
    $this->store = Store::factory()->create();
    $this->warehouse = Warehouse::factory()->create();

    $this->storeSetting = StoreSetting::factory()->create([
        'store_id' => $this->store->id,
        'key' => StoreSetting::KEY_SHIPX_API_KEY,
        'value' => 'test_api_key_123'
    ]);

    $this->saleOrder = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'order_number' => 'TEST-ORDER-001',
        'order_status' => 'new'
    ]);

    $this->saleOrderAddress = SaleOrderAddress::factory()->create([
        'order_id' => $this->saleOrder->id,
        'name' => 'Test Customer',
        'street1' => '123 Test St',
        'city' => 'Test City',
        'state' => 'CA',
        'zip' => '94103',
        'country' => 'US',
        'phone' => '4151234567',
        'email' => '<EMAIL>'
    ]);

    $this->shipment = Shipment::factory()->create([
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id,
        'dimension_length' => 10.5,
        'dimension_width' => 8.0,
        'dimension_height' => 3.0,
        'weight_value' => 2.5,
        'service_code' => 'USPS_PRIORITY'
    ]);

    // Mock Google Chat setting
    Setting::factory()->create([
        'name' => Setting::GOOGLE_SPACE_SHIPX_RATE_ERROR,
        'value' => 'test_google_chat_webhook'
    ]);
});

test('job handles successful shipx rate request', function () {
    // Mock successful HTTP response
    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'rates' => [
                [
                    'service' => 'USPS_PRIORITY',
                    'rate' => 15.50
                ],
                [
                    'service' => 'USPS_GROUND',
                    'rate' => 12.30
                ]
            ]
        ], 200)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn([
            'name' => 'Test Customer',
            'street1' => '123 Test St',
            'city' => 'Test City',
            'state' => 'CA',
            'zip' => '94103',
            'country' => 'US',
            'phone' => '4151234567',
            'email' => '<EMAIL>'
        ]);

        $mock->shouldReceive('dataAddressFrom')->andReturn([
            'name' => 'Test Warehouse',
            'street1' => '456 Warehouse Ave',
            'city' => 'Warehouse City',
            'state' => 'CA',
            'zip' => '94104',
            'country' => 'US'
        ]);

        $mock->shouldReceive('dataAddressReturn')->andReturn([
            'name' => 'Return Address',
            'street1' => '789 Return St',
            'city' => 'Return City',
            'state' => 'CA',
            'zip' => '94105',
            'country' => 'US'
        ]);
    });

    // Execute the job
    $job = new GetRateShipXJob($this->shipment->id);
    $job->handle();


//    dd(Shipment::all()->toArray());
    // Assert shipment was updated with rate
    $this->shipment->refresh();
    expect($this->shipment->shipx_rate)->toEqual('15.50');

    // Assert HTTP request was made with correct data
    Http::assertSent(function ($request) {
        return $request->url() === 'https://shipx-api.swiftpod.com/v1/shipments'
               && $request->header('Authorization')[0] === 'Bearer test_api_key_123'
               && $request->header('Content-Type')[0] === 'application/json';
    });
});

test('job handles shipment not found gracefully', function () {
    $job = new GetRateShipXJob(999999); // Non-existent shipment ID

    // Job should not throw exception, but handle it gracefully
    $job->handle();

    // Check that error was captured
    $lastError = $job->getLastError();
    expect($lastError)->not->toBeNull();
    expect($lastError['error_message'])->toContain('Shipment not found');
    expect($lastError['shipment_id'])->toBe(999999);
});

test('job handles store setting not found gracefully', function () {
    // Delete the store setting
    $this->storeSetting->delete();

    $job = new GetRateShipXJob($this->shipment->id);

    // Job should handle exception gracefully
    $job->handle();

    // Check that error was captured
    $lastError = $job->getLastError();
    expect($lastError)->not->toBeNull();
    expect($lastError['error_message'])->toContain('Store setting not found');
});

test('job handles sale order not found gracefully', function () {
    // Delete the sale order
    $this->saleOrder->delete();

    $job = new GetRateShipXJob($this->shipment->id);

    // Job should handle exception gracefully
    $job->handle();

    expect(true)->toBeTrue();
});

test('job throws exception when shipx returns error status', function () {
    // Mock error HTTP response
    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'error' => 'Invalid API key'
        ], 401)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);

    // Job should handle exception gracefully
    $job->handle();

    expect(true)->toBeTrue();
});

test('job throws exception when no rates found', function () {
    // Mock response with empty rates
    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'rates' => []
        ], 200)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);

    // Job should handle exception gracefully
    $job->handle();

    expect(true)->toBeTrue();
});

test('job handles service code not found gracefully', function () {
    // Mock response with rates but different service codes
    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'rates' => [
                [
                    'service' => 'FEDEX_GROUND',
                    'rate' => 20.00
                ]
            ]
        ], 200)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);

    // Job should handle exception gracefully
    $job->handle();

    expect(true)->toBeTrue();
});

test('sendRequestShipX method makes correct HTTP request', function () {
    Http::fake();

    $job = new GetRateShipXJob($this->shipment->id);
    $data = ['test' => 'data'];
    $token = 'test_token';
    $url = 'https://test-api.com/endpoint';

    // Use reflection to access protected method
    $reflection = new ReflectionClass($job);
    $method = $reflection->getMethod('sendRequestShipX');
    $method->setAccessible(true);

    $method->invoke($job, $url, $data, $token);

    Http::assertSent(function ($request) use ($url, $data, $token) {
        return $request->url() === $url
               && $request->header('Authorization')[0] === 'Bearer ' . $token
               && $request->header('Content-Type')[0] === 'application/json'
               && $request->header('Accept')[0] === 'application/json'
               && $request->data() === $data;
    });
});

test('job handles international shipping with customs info', function () {
    // Update address to international
    $this->saleOrderAddress->update(['country' => 'CA']); // Canada

    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'rates' => [
                [
                    'service' => 'USPS_PRIORITY',
                    'rate' => 25.50
                ]
            ]
        ], 200)
    ]);

    // Mock LabelRepository with customs info
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn([
            'country' => 'CA',
            'city' => 'Toronto',
            'state' => 'ON'
        ]);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
        $mock->shouldReceive('dataCustomsInfoV2')->andReturn([
            'customs_items' => [
                [
                    'description' => 'Test Item',
                    'value' => 10.00,
                    'weight' => 1.0
                ]
            ]
        ]);
    });

    $job = new GetRateShipXJob($this->shipment->id);
    $job->handle();

    $this->shipment->refresh();
    expect($this->shipment->shipx_rate)->toBe('25.50');
});

test('job handles timeout correctly', function () {
    // Mock timeout response
    Http::fake([
        'shipx-api.swiftpod.com/*' => function () {
            throw new \Illuminate\Http\Client\ConnectionException('Connection timeout');
        }
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);

    // Job should handle exception gracefully and not throw
    $job->handle();

    // Shipment should not be updated with rate (or remain as default)
    $this->shipment->refresh();
    // Since job handles exception gracefully, shipment rate might be 0.00 or null
    expect($this->shipment->shipx_rate)->toBeIn([null, '0.00', 0]);
});

test('job uses default dimensions when shipment dimensions are null', function () {
    // Update shipment with null dimensions
    $this->shipment->update([
        'dimension_length' => null,
        'dimension_width' => null,
        'dimension_height' => null,
        'weight_value' => null,
    ]);

    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'rates' => [
                [
                    'service' => 'USPS_PRIORITY',
                    'rate' => 15.50
                ]
            ]
        ], 200)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);
    $job->handle();

    // Assert HTTP request was made with default dimensions
    Http::assertSent(function ($request) {
        $data = $request->data();

        return $data['parcel']['length'] === 9
               && $data['parcel']['width'] === 6
               && $data['parcel']['height'] === 2
               && $data['parcel']['weight'] === 0;
    });
});

test('job includes tax identifiers when ioss number exists', function () {
    // Update sale order with IOSS number - disable events to avoid order_status issue
    SaleOrder::withoutEvents(function () {
        $this->saleOrder->update([
            'ioss_number' => 'IM1234567890',
            'tax_id_type' => 'IOSS'
        ]);
    });

    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'rates' => [
                [
                    'service' => 'USPS_PRIORITY',
                    'rate' => 15.50
                ]
            ]
        ], 200)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'DE']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
        $mock->shouldReceive('dataCustomsInfoV2')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);
    $job->handle();

    // Assert HTTP request includes tax identifiers
    Http::assertSent(function ($request) {
        $data = $request->data();

        return isset($data['tax_identifiers'])
               && $data['tax_identifiers'][0]['entity'] === 'SENDER'
               && $data['tax_identifiers'][0]['tax_id'] === 'IM1234567890'
               && $data['tax_identifiers'][0]['tax_id_type'] === 'IOSS';
    });
});

test('job throws exception for critical API errors', function () {
    // Mock critical API error (500 status)
    Http::fake([
        'shipx-api.swiftpod.com/*' => Http::response([
            'error' => 'Internal Server Error'
        ], 500)
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);

    // Critical API errors should throw exception
    expect(fn() => $job->handle())
        ->toThrow(ShipXException::class, 'ShipX error: Internal Server Error');
});

test('job can retrieve specific error types', function () {
    $job = new GetRateShipXJob(999999); // Non-existent shipment ID

    $job->handle();

    $lastError = $job->getLastError();
    expect($lastError['error_message'])->toContain('Shipment not found');
    expect($lastError['shipment_id'])->toBe(999999);
    expect($lastError['job_class'])->toBe(GetRateShipXJob::class);
    expect($lastError['timestamp'])->not->toBeNull();
});

test('shipx exception contains proper context', function () {
    $exception = ShipXException::serviceCodeMismatch('USPS_PRIORITY', 123, 'ORDER-001');

    expect($exception->getShipmentId())->toBe(123);
    expect($exception->getErrorType())->toBe('service_code_mismatch');
    expect($exception->getContext())->toHaveKey('service_code');
    expect($exception->getContext()['service_code'])->toBe('USPS_PRIORITY');
    expect($exception->getMessage())->toContain('USPS_PRIORITY');
    expect($exception->getMessage())->toContain('ORDER-001');
});

test('job handles connection timeout as critical error', function () {
    // Mock connection timeout
    Http::fake([
        'shipx-api.swiftpod.com/*' => function () {
            throw new \Illuminate\Http\Client\ConnectionException('Connection timeout');
        }
    ]);

    // Mock LabelRepository
    $this->mock(LabelRepository::class, function ($mock) {
        $mock->shouldReceive('dataAddressTo')->andReturn(['country' => 'US']);
        $mock->shouldReceive('dataAddressFrom')->andReturn([]);
        $mock->shouldReceive('dataAddressReturn')->andReturn([]);
    });

    $job = new GetRateShipXJob($this->shipment->id);

    // Connection errors should throw exception (critical error)
    expect(fn() => $job->handle())
        ->toThrow(\Illuminate\Http\Client\ConnectionException::class);
});
