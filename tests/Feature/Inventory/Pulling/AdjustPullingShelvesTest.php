<?php

use App\Models\Country;
use App\Models\Employee;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PartNumber;
use App\Models\PartNumberFifo;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\TimeTracking;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->product = Product::factory()->create([
        'sku' => 'UNGH1M0XL',
        'color' => 'HEATHER GREY',
        'size' => 'L',
        'style' => '340',
        'parent_id' => 2,
    ]);
    $this->timeTracking = TimeTracking::factory()->create();
    $this->country = Country::factory(['iso2' => 'US'])->create();

    $this->purchaseOrderFirst = PurchaseOrder::factory(['order_date' => '2022-04-30'])->has(
        PurchaseOrderItem::factory([
            'quantity' => 3,
            'price' => 2.0,
            'product_id' => $this->product->id,
        ]),
        'items',
    )->create();

    $this->purchaseOrderLasted = PurchaseOrder::factory(['order_date' => '2022-06-01'])->has(
        PurchaseOrderItem::factory([
            'quantity' => 3,
            'price' => 5.0,
            'product_id' => $this->product->id,
        ]),
        'items',
    )->create();

    $this->endpoint = '/api/adjust-pulling-shelves';

    $this->params = [
        'product_style' => $this->product->style,
        'product_color' => $this->product->color,
        'product_size' => $this->product->size,
        'quantity' => 1,
        'employee_id' => $this->employee->code,
        'id_time_checking' => $this->timeTracking->id,
        'country' => $this->country->iso2,
    ];

    $this->warehouseMexico = Warehouse::WAREHOUSE_MEXICO;

    $this->partNumber = PartNumber::factory(
        ['product_id' => $this->product->id, 'country' => $this->country->iso2],
    )
        ->has(PartNumberFifo::factory([
            'quantity' => 20,
            'warehouse_id' => $this->warehouseMexico[0],
            'product_id' => $this->product->id,
        ]), 'partNumberFifos')
        ->create();
});

test('adjust pulling shelves failed - missing product_style', function () {
    unset($this->params['product_style']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'product_style' => ['The product style field is required.']
        ]
    ]);
});

test('adjust pulling shelves failed - missing product_color', function () {
    unset($this->params['product_color']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'product_color' => ['The product color field is required.']
        ]
    ]);
});

test('adjust pulling shelves failed - missing product_size', function () {
    unset($this->params['product_size']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'product_size' => ['The product size field is required.']
        ]
    ]);
});

test('adjust pulling shelves failed - missing quantity', function () {
    unset($this->params['quantity']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'quantity' => ['The quantity field is required.']
        ]
    ]);
});
test('adjust pulling shelves failed - The quantity must be at least 0.', function () {
    $this->params['quantity'] = '-1';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'quantity' => [
                'The quantity must be at least 0.',
            ]
        ]
    ]);
});

test('adjust pulling shelves failed - missing employee_id', function () {
    unset($this->params['employee_id']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => ['The employee id field is required.']
        ]
    ]);
});

test('adjust pulling shelves failed - The selected employee id is invalid.', function () {
    $employee = Employee::factory()->create(['warehouse_id' => 2, 'code' => 2022]);
    $this->params['employee_id'] = $employee->code;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => ['The selected employee id is invalid.']
        ]
    ]);
});

test('adjust pulling shelves failed - missing id_time_checking', function () {
    unset($this->params['id_time_checking']);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'id_time_checking' => ['The id time checking field is required.']
        ]
    ]);
});

test('adjust pulling shelves failed - The product is not found or has been deleted', function () {
    $this->params['product_style'] = 'Hoodie';

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The product is not found or has been deleted!'
    ]);
});

test('adjust pulling shelves failed - Pulling shelves location is not found or has been deleted!', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Pulling shelves location is not found or has been deleted!'
    ]);
});

test('adjust pulling shelves failed - The quantity is matched, so no need to adjust!', function () {
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    LocationProduct::factory()->create(['location_id' => $location->id, 'product_id' => $this->product->id, 'quantity' => $this->params['quantity']]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The quantity is matched, so no need to adjust!'
    ]);
});

test('adjust pulling shelves SUCCESS', function () {
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    LocationProduct::factory()->create(['location_id' => $location->id, 'product_id' => $this->product->id, 'quantity' => 10]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(200)->assertJsonStructure([
        'message',
        'data' => [
            'user_id',
            'warehouse_id',
            'product_id',
            'sku',
            'product_available',
            'product_on_hand',
            'product_adjust',
            'employee_id',
            'updated_at',
            'created_at',
            'id',

        ]
    ]);
    $responseData = json_decode($response->getContent());
    $this->assertDatabaseHas('adjust_pulling_shelves', ['id' => $responseData->data->id]);
    $poItem = PurchaseOrderItem::where('po_id', $this->purchaseOrderLasted->id)
        ->where('product_id', $this->product->id)
        ->first();
    $this->assertEquals($responseData->data->cost_value_on_hand, $poItem->price * $responseData->data->product_on_hand);
    $this->assertEquals($responseData->data->cost_value_adjusted, $poItem->price * $responseData->data->product_adjust);
    $this->assertEquals($responseData->data->cost_value_available, $poItem->price * $responseData->data->product_available);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);
});

//country not found warehouse mexico
test('adjust pulling shelves failed - warehouse mexico: country not found', function () {
    $token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouseMexico[0]]])->fromUser($this->user);
    Employee::query()->where('id', $this->employee->id)->update(['warehouse_id' => $this->warehouseMexico[0]]);
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouseMexico[0], 'barcode' => 'LO123']);
    LocationProduct::factory()->create(['location_id' => $location->id, 'product_id' => $this->product->id, 'quantity' => 10]);
    Country::query()->delete();

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
})->skip('no need anymore');

//part number not found warehouse mexico
test('adjust pulling shelves failed - warehouse mexico: part number not found', function () {
    $token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouseMexico[0]]])->fromUser($this->user);
    Employee::query()->where('id', $this->employee->id)->update(['warehouse_id' => $this->warehouseMexico[0]]);
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouseMexico[0], 'barcode' => 'LO123']);
    LocationProduct::factory()->create(['location_id' => $location->id, 'product_id' => $this->product->id, 'quantity' => 10]);
    PartNumber::query()->delete();

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
})->skip('no need anymore');

//warehouse mexico
test('adjust pulling shelves SUCCESS - warehouse mexico', function () {
    $token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouseMexico[0]]])->fromUser($this->user);
    Employee::query()->where('id', $this->employee->id)->update(['warehouse_id' => $this->warehouseMexico[0]]);
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouseMexico[0], 'barcode' => 'LO123']);
    LocationProduct::factory()->create(['location_id' => $location->id, 'product_id' => $this->product->id, 'quantity' => 10]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(200)->assertJsonStructure([
        'message',
        'data' => [
            'user_id',
            'warehouse_id',
            'product_id',
            'sku',
            'product_available',
            'product_on_hand',
            'product_adjust',
            'employee_id',
            'updated_at',
            'created_at',
            'id',

        ]
    ]);
    $responseData = json_decode($response->getContent());
    $this->assertDatabaseHas('adjust_pulling_shelves', ['id' => $responseData->data->id]);
    $poItem = PurchaseOrderItem::where('po_id', $this->purchaseOrderLasted->id)
        ->where('product_id', $this->product->id)
        ->first();
    $this->assertEquals($responseData->data->cost_value_on_hand, $poItem->price * $responseData->data->product_on_hand);
    $this->assertEquals($responseData->data->cost_value_adjusted, $poItem->price * $responseData->data->product_adjust);
    $this->assertEquals($responseData->data->cost_value_available, $poItem->price * $responseData->data->product_available);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);
});
