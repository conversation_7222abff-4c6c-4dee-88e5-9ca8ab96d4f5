<?php

use App\Models\Box;
use App\Models\Employee;
use App\Models\InternalRequest;
use App\Models\InternalRequestHistory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\TimeChecking;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->quantity = 10;
    $this->product = Product::factory()->count(2)->sequence(
        [
            'sku' => 'UNPT9C0XS',
            'style' => '3001',
            'color' => 'AQUA',
            'size' => 'XS',
        ],
        [
            'sku' => 'UNPT9C2XS',
            'style' => '3001',
            'color' => 'AQUA',
            'size' => '2XS',
        ])->create();
    $this->employee = Employee::factory()->count(2)->create();
    $this->internalRequest = InternalRequest::create([
        'warehouse_id' => 1,
        'product_id' => $this->product[0]->id,
        'employee_id' => $this->employee[0]->id,
        'status' => InternalRequest::NEW_STATUS

    ]);
    $this->location = Location::factory()->count(3)->sequence(
        [
            'type' => Location::RACK,
            'warehouse_id' => 1
        ],
        [
            'type' => Location::PULLING_SHELVES,
            'warehouse_id' => 1
        ],
        [
            'type' => Location::PENDING_TYPE,
            'warehouse_id' => 1
        ],
    )->create();
    $this->box_quantity = 10;
    $this->locationProduct = LocationProduct::factory()->count(2)->sequence(
        [
            'location_id' => $this->location[0]->id,
            'product_id' => $this->product[0]->id,
            'quantity' => $this->box_quantity
        ],
        [
            'location_id' => $this->location[1]->id,
            'product_id' => $this->product[0]->id,
            'quantity' => 0
        ],
    )->create();
    $this->box = Box::factory()->count(2)->sequence(
        [
            'warehouse_id' => 1,
            'location_id' => $this->location[0]->id,
            'product_id' => $this->product[0]->id,
            'quantity' => $this->box_quantity,
            'is_deleted' => Box::NOT_DELETED
        ],
        [
            'warehouse_id' => 1,
            'location_id' => $this->location[0]->id,
            'product_id' => $this->product[1]->id,
            'quantity' => $this->box_quantity,
            'is_deleted' => Box::NOT_DELETED
        ],
    )->create();
    \App\Models\BoxMoving::factory()->create([
        'box_id' => $this->box[0]->id,
        'location_id' => $this->location[0]->id,
        'warehouse_id' => 1,
        'product_id' => $this->product[0]->id,
        'quantity' => $this->box_quantity,
        'employee_id' => $this->employee[1]->id,
        'created_at' => date('Y-m-d H:i:s'),
    ]);

    $this->timeChecking = TimeChecking::factory()->create([
        'employee_id' => $this->employee[1]->id,
        'start_time' => now(),
        'end_time' => now(),
        'job_type' => 'create_box_moving',
        'quantity' => 0
    ]);
    $this->endpoint = '/api/internal-request/confirm-request/' . $this->internalRequest->id;
    $this->params = [
        'employee_id' => $this->employee[1]->id,
        'box_id' => $this->box[0]->barcode,
    ];
});

test('Confirm fail: internal request not found.', function () {
    InternalRequest::find($this->internalRequest->id)->delete();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Internal request not found.'
    ],
    );
});

test('Confirm fail: Employee is not found or deleted.', function () {
    $employee = Employee::find($this->params['employee_id']);
    $employee->is_deleted = 1;
    $employee->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Employee is not found or deleted.'
    ],
    );
});

test('Confirm fail: Invalid barcode.', function () {
    $internalRequest = InternalRequest::find($this->internalRequest->id);
    $internalRequest->status = InternalRequest::NEW_STATUS;
    $internalRequest->box_id = $this->box[1]->id;
    $internalRequest->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Invalid barcode. Please try again.'
    ],
    );
});

test('Confirm fail: internal request is not uncheck status.', function () {
    $internalRequest = InternalRequest::find($this->internalRequest->id);
    $internalRequest->status = InternalRequest::NEW_STATUS;
    $internalRequest->box_id = $this->box[0]->id;
    $internalRequest->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Only "Uncheck" request can be confirmed by Pulling team.'
    ],
    );
});

test('Confirm success.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->employee[1]->id
    ]);
    $this->put('/api/internal-request/' . $this->internalRequest->id, [
        'employee_id' => $this->employee[1]->id,
        'barcode' => $this->box[0]->barcode,
        'id_time_checking' => $this->timeChecking,

    ]);
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    $this->assertEquals($data['message'], 'Request confirmed successfully.');
    expect($data['data'])->toMatchArray([
        'product_id' => $this->product[0]->id,
        'box_id' => $this->box[0]->id,
        'employee_confirm_id' => $this->params['employee_id'],
        'status' => InternalRequest::CHECKED_STATUS,
    ],
    );
    $this->assertDatabaseHas('box', [
        'id' => $this->box[0]->id,
        'warehouse_id' => $this->box[0]->warehouse_id,
        'is_deleted' => 1
    ]);
    $history = InternalRequestHistory::query()
        ->where('internal_request_id', $this->internalRequest->id)
        ->where('action', InternalRequest::CONFIRM_TYPE)
        ->where('employee_id', $this->params['employee_id'])
        ->exists();
    $this->assertTrue($history);
});
