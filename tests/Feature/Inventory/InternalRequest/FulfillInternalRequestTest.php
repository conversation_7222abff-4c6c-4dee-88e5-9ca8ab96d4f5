<?php

use App\Models\Box;
use App\Models\Employee;
use App\Models\InternalRequest;
use App\Models\InternalRequestHistory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\TimeChecking;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->quantity = 10;
    $this->product = Product::factory()->count(2)->sequence(
        [
            'sku' => 'UNPT9C0XS',
            'style' => '3001',
            'color' => 'AQUA',
            'size' => 'XS',
        ],
        [
            'sku' => 'UNPT9C2XS',
            'style' => '3001',
            'color' => 'AQUA',
            'size' => '2XS',
        ])->create();
    $this->employee = Employee::factory()->count(2)->create();
    $this->internalRequest = InternalRequest::create([
        'warehouse_id' => 1,
        'product_id' => $this->product[0]->id,
        'employee_id' => $this->employee[0]->id,
        'status' => InternalRequest::NEW_STATUS

    ]);
    $this->location = Location::factory()->count(3)->sequence(
        [
            'type' => Location::RACK,
            'warehouse_id' => 1
        ],
        [
            'type' => Location::PULLING_SHELVES,
            'warehouse_id' => 1
        ],
        [
            'type' => Location::PENDING_TYPE,
            'warehouse_id' => 1
        ],
    )->create();
    $this->box_quantity = 10;
    $this->locationProduct = LocationProduct::factory()->count(2)->sequence(
        [
            'location_id' => $this->location[0]->id,
            'product_id' => $this->product[0]->id,
            'quantity' => $this->box_quantity
        ],
        [
            'location_id' => $this->location[1]->id,
            'product_id' => $this->product[0]->id,
            'quantity' => 0
        ],
    )->create();
    $this->box = Box::factory()->count(2)->sequence(
        [
            'warehouse_id' => 1,
            'location_id' => $this->location[0]->id,
            'product_id' => $this->product[0]->id,
            'quantity' => $this->box_quantity,
            'is_deleted' => Box::NOT_DELETED
        ],
        [
            'warehouse_id' => 1,
            'location_id' => $this->location[0]->id,
            'product_id' => $this->product[1]->id,
            'quantity' => $this->box_quantity,
            'is_deleted' => Box::NOT_DELETED
        ],
    )->create();
    \App\Models\BoxMoving::factory()->create([
        'box_id' => $this->box[0]->id,
        'location_id' => $this->location[0]->id,
        'warehouse_id' => 1,
        'product_id' => $this->product[0]->id,
        'quantity' => $this->box_quantity,
        'employee_id' => $this->employee[1]->id,
        'created_at' => date('Y-m-d H:i:s'),
    ]);

    $this->timeChecking = TimeChecking::factory()->create([
        'employee_id' => $this->employee[1]->id,
        'start_time' => now(),
        'end_time' => now(),
        'job_type' => 'create_box_moving',
        'quantity' => 0
    ]);
    $this->endpoint = '/api/internal-request/' . $this->internalRequest->id;
    $this->params = [
        'employee_id' => $this->employee[1]->id,
        'barcode' => $this->box[0]->barcode,
        'id_time_checking' => $this->timeChecking,

    ];
});

test('Fulfill fail: internal request not found.', function () {
    InternalRequest::find($this->internalRequest->id)->delete();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Internal request not found.'
    ],
    );
});
test('Fulfill fail: internal request is not picking_up status.', function () {
    $internalRequest = InternalRequest::find($this->internalRequest->id);
    $internalRequest->status = InternalRequest::NEW_STATUS;
    $internalRequest->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Only "Picking Up" request can be fulfilled.'
    ],
    );
});

test('Fulfill fail: This request was picked up by another employee.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->employee[0]->id
    ]);

    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'This request was picked up by another employee.'
    ],
    );
});

test('Fulfill fail: Employee is not found or deleted.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->params['employee_id']
    ]);
    $employee = Employee::find($this->params['employee_id']);
    $employee->is_deleted = 1;
    $employee->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Employee is not found or deleted.'
    ],
    );
});

test('Fulfill fail: Fulfill this request as it belongs to a another warehouse.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->params['employee_id']
    ]);
    $internalRequest = InternalRequest::find($this->internalRequest->id);
    $internalRequest->warehouse_id = 2;
    $internalRequest->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'You cannot fulfill this request as it belongs to a another warehouse.'
    ],
    );
});

test('Fulfill fail: Box is not found or deleted.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->params['employee_id']
    ]);
    $box = Box::find($this->box[0]->id);
    $box->is_deleted = 1;
    $box->save();
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Box is not found or deleted.'
    ],
    );
});

test('Fulfill fail: Product SKU in Box ID does not match the requested SKU.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->params['employee_id']
    ]);
    $this->params['barcode'] = $this->box[1]->barcode;
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Error. Product SKU in Box ID does not match the requested SKU.'
    ],
    );
});

test('Fulfill fail: This box is moving to pulling shelves.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->params['employee_id']
    ]);
    unset($this->location[0]);
    foreach ($this->location as $location) {
        $box = Box::find($this->box[0]->id);
        $box->location_id = $location->id;
        $box->save();
        $response = $this->put($this->endpoint, $this->params);
        $data = json_decode($response->getContent(), true);
        expect($data)->toMatchArray([
            'message' => "This box is moving to pulling shelves. You can't move it to another location."
        ],
        );
    }
});

test('Fulfill success.', function () {
    $this->put('/api/internal-request/receive-request/' . $this->internalRequest->id, [
        'employee_id' => $this->params['employee_id']
    ]);
    $response = $this->put($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    $this->assertEquals($data['message'], 'Request fulfilled successfully.');
    expect($data['data'])->toMatchArray([
        'product_id' => $this->product[0]->id,
        'box_id' => $this->box[0]->id,
        'employee_fulfill_id' => $this->params['employee_id'],
        'status' => InternalRequest::UNCHECK_STATUS,
    ],
    );
    $history = InternalRequestHistory::query()
        ->where('internal_request_id', $this->internalRequest->id)
        ->where('action', InternalRequest::FULFILL_TYPE)
        ->where('employee_id', $this->params['employee_id'])
        ->exists();
    $this->assertTrue($history);
});
