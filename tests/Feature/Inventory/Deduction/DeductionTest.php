<?php

use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\User;
use App\Models\Warehouse;
use Carbon\Carbon;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/inventory-deduction';
    $this->product = Product::factory()->create(['sku' => 'UNGH1M0XL', 'gtin' => '00821780067052']);
    $this->location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->params = [];
});

// missing sale_order_sku
test('deduction failed - missing sale_order_sku', function () {
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});

//sale_order_sku là label_id, kiểm tra label_id có tồn tại trong sale_order_item_barcode không ? nếu không thì dừng deduction
test('deduction failed - Label not found (sale_order_sku is a label id)', function () {
    $this->params = [
        'sale_order_sku' => '080922-SJ-M-000044-2',
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});

//sale_order_sku không là sku
test('SKU not found (sale_order_sku is a sku)- assert status code = 422', function () {
    $this->params = [
        'sale_order_sku' => faker::create()->unique()->text(9),
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});

//Order phải có trạng thái khác : draft, rejected, on_hold, cancelled mới được deduction
test('deduction failed - The order has been: draft, rejected, on_hold, cancelled', function () {
    $orderStatus = [
        SaleOrder::DRAFT,
        SaleOrder::REJECTED,
        SaleOrder::ON_HOLD,
        SaleOrder::CANCELLED,
        SaleOrder::STATUS_LATE_CANCELLED
    ];

    foreach ($orderStatus as $status) {
        $this->saleOrder = SaleOrder::factory([
            'order_status' => $status
        ])->has(SaleOrderItem::factory([
            'product_id' => $this->product->id,
        ])->has(SaleOrderItemBarcode::factory([
            'warehouse_id' => $this->warehouse->id,
            'label_id' => '080922-SJ-M-000044-2',
        ]), 'barcodes'), 'items')
            ->create();
        $this->saleOrder->items->map(function ($item) {
            return $item->barcodes->map(function ($barcode) {
                $barcode->order_id = $this->saleOrder->id;
                $barcode->save();
            });
        });
        $this->params['sale_order_sku'] = '080922-SJ-M-000044-2';

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $this->params);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
    }
});
//sale_order_sku là label_id, Deduction thành công
test('deduction success, sale_order_sku là label_id', function () {
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);

    $this->params = [
        'sale_order_sku' => '080922-SJ-M-000044-2',
    ];

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201)->assertJsonStructure(
        [
            'id',
            'warehouse_id',
            'location_id',
            'user_id',
            'is_deleted',
            'quantity',
            'employee_id',
            'product_id',
            'sale_order_id',
            'sale_order_sku',
            'deduction_csv_id',
            'label_id',
            'is_duplicate',
            'wip_product_id',
            'created_at',
            'updated_at',
            'country',
            'box',
            'product' => [
                'id',
                'parent_id',
                'name',
                'style',
                'sku',
                'gtin',
                'gtin_case',
                'cost',
                'price',
                'color',
                'size',
                'description',
                'tag',
                'created_at',
                'updated_at',
                'in_stock',
                'is_deleted',
                'display_order',
                'brand_id',
                'is_default',
                'user_id',
                'category_ids',
                'image',
                'is_popular',
            ]
        ]);

    //Assert them inventory_deduction thanh cong
    $deduction = InventoryDeduction::where('label_id', $this->params['sale_order_sku'])->first();
    $this->assertNotEmpty($deduction);

    //Assert them inventory thanh cong
    $inventory = Inventory::where('object_id', $deduction->id)->first();
    $this->assertNotEmpty($inventory);

    //Assert pulled_at  trong sale_order_item_barcode được update now
    $saleOrderItemBarcode = SaleOrderItemBarcode::where('label_id', $this->params['sale_order_sku'])->first();
    $this->assertEquals(Carbon::parse($saleOrderItemBarcode->pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert Sale Order : pulled_at = now
    $saleOrder = SaleOrder::where('id', $saleOrderItemBarcode->order_id)->first();
    $this->assertEquals($saleOrder->order_pulled_status, 1);
    $this->assertEquals(Carbon::parse($saleOrder->order_pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert quantity -= 1
    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity - 1);

    //Assert quantity -= 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $locationQuantityBeforeTest->quantity - 1);

    //assert history order timeline
    $this->assertDatabaseHas('sale_order_history', ['order_id' => $this->saleOrder->id, 'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE]);
});

//sale_order_sku là sku
test('deduction success - sale_order_sku là sku', function () {
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => $this->product->sku,
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201)->assertJsonStructure(
        [
            'id',
            'warehouse_id',
            'location_id',
            'user_id',
            'is_deleted',
            'quantity',
            'employee_id',
            'product_id',
            'sale_order_id',
            'sale_order_sku',
            'deduction_csv_id',
            'label_id',
            'is_duplicate',
            'wip_product_id',
            'created_at',
            'updated_at',
            'country',
            'box',
            'product' => [
                'id',
                'parent_id',
                'name',
                'style',
                'sku',
                'gtin',
                'gtin_case',
                'cost',
                'price',
                'color',
                'size',
                'description',
                'tag',
                'created_at',
                'updated_at',
                'in_stock',
                'is_deleted',
                'display_order',
                'brand_id',
                'is_default',
                'user_id',
                'category_ids',
                'image',
                'is_popular',
            ]
        ]);

    //Assert them inventory_deduction thanh cong
    $deduction = InventoryDeduction::where('sale_order_sku', $this->params['sale_order_sku'])->first();
    $this->assertNotEmpty($deduction);
    //Assert them inventory thanh cong
    $inventory = Inventory::where('object_id', $deduction->id)->first();
    $this->assertNotEmpty($inventory);
    //Assert quantity -= 1
    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity - 1);

    //Assert quantity -= 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $locationQuantityBeforeTest->quantity - 1);
});

// deduction fail with Mexico - barcode box required
test('deduction fail with Mexico - barcode box required', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => '080922-MX-M-000044-2',
    ];
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'The box barcode field is required.'
        ]
    ]);
});

// deduction fail with Mexico - Box not found
test('deduction fail with Mexico - Box not found', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => '080922-MX-M-000044-2',
        'box_barcode' => '123456789'
    ];
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'Box not found'
        ]
    ]);
});

// deduction fail with Mexico - Inventory addition not found
test('deduction fail with Mexico - Inventory addition not found', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'barcode' => 'AGSBTCU',
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => '080922-MX-M-000044-2',
        'box_barcode' => $box->barcode
    ];
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'Inventory addition not found'
        ]
    ]);

    // TH country null
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => null
    ])->create();
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'Inventory addition not found'
        ]
    ]);
});

// deduction fail with Mexico - This SKU and country of origin do not have a corresponding part number.
test('deduction fail with Mexico - This SKU and country of origin do not have a corresponding part number', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'barcode' => 'AGSBTCU',
    ])->create();
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => 'VN'
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => '080922-MX-M-000044-2',
        'box_barcode' => $box->barcode
    ];
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'sale_order_sku' => [
            'This SKU and country of origin do not have a corresponding part number.'
        ]
    ]);

    $partNumber = \App\Models\PartNumber::factory([
        'product_id' => $this->product->id,
        'country' => 'AB',
    ])->create();
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'sale_order_sku' => [
            'This SKU and country of origin do not have a corresponding part number.'
        ]
    ]);
});

// deduction fail with Mexico - The scanned box must contain the product corresponding to the Label ID.
test('deduction fail with Mexico - The scanned box must contain the product corresponding to the Label ID', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'product_id' => 997788,
        'barcode' => 'AGSBTCU',
    ])->create();
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => 'VN'
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => '080922-MX-M-000044-2',
        'box_barcode' => $box->barcode
    ];
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $partNumber = \App\Models\PartNumber::factory([
        'product_id' => $this->product->id,
        'country' => 'AB',
    ])->create();
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'The scanned box must contain the product corresponding to the Label ID'
        ]
    ]);
});

// deduction success with Mexico.
test('deduction success with Mexico', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'barcode' => 'AGSBTCU',
    ])->create();
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => 'VN'
    ])->create();
    $partNumber = \App\Models\PartNumber::factory([
        'product_id' => $this->product->id,
        'country' => $addition->country,
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'sale_order_sku' => '080922-MX-M-000044-2',
        'box_barcode' => $box->barcode
    ];
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $this->product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(201)->assertJsonStructure(
        [
            'id',
            'warehouse_id',
            'location_id',
            'user_id',
            'is_deleted',
            'quantity',
            'employee_id',
            'product_id',
            'sale_order_id',
            'sale_order_sku',
            'deduction_csv_id',
            'label_id',
            'is_duplicate',
            'wip_product_id',
            'created_at',
            'updated_at',
            'country',
            'box',
            'product' => [
                'id',
                'parent_id',
                'name',
                'style',
                'sku',
                'gtin',
                'gtin_case',
                'cost',
                'price',
                'color',
                'size',
                'description',
                'tag',
                'created_at',
                'updated_at',
                'in_stock',
                'is_deleted',
                'display_order',
                'brand_id',
                'is_default',
                'user_id',
                'category_ids',
                'image',
                'is_popular',
            ]
        ]);

    //Assert them inventory_deduction thanh cong
    $deduction = InventoryDeduction::where('label_id', $this->params['sale_order_sku'])->where('coo_iso2', $addition->country)->where('box_id', $box->id)->first();
    $this->assertNotEmpty($deduction);

    //Assert them inventory thanh cong
    $inventory = Inventory::where('object_id', $deduction->id)->first();
    $this->assertNotEmpty($inventory);

    //Assert pulled_at  trong sale_order_item_barcode được update now
    $saleOrderItemBarcode = SaleOrderItemBarcode::where('label_id', $this->params['sale_order_sku'])->where('part_number_id', $partNumber->id)->first();
    $this->assertEquals(Carbon::parse($saleOrderItemBarcode->pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert Sale Order : pulled_at = now
    $saleOrder = SaleOrder::where('id', $saleOrderItemBarcode->order_id)->first();
    $this->assertEquals($saleOrder->order_pulled_status, 1);
    $this->assertEquals(Carbon::parse($saleOrder->order_pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert quantity -= 1
    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity - 1);

    //Assert quantity -= 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $locationQuantityBeforeTest->quantity - 1);
});

// deduction warning with sku CUSTNLXXX
test('deduction warning - sale_order sample with sku CUSTNLXXX', function () {
    $this->params = [
        'sale_order_sku' => '080922-SJ-M-000044-2',
    ];
    $product = Product::factory()->create(['sku' => 'CUSTNLXXX']);
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $product->id,
        'product_sku' => $product->sku,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'sku' => 'MTk3MTc4OA==' . $product->sku,
        'label_id' => $this->params['sale_order_sku'],
    ]), 'barcodes'), 'items')
        ->create();
    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'warning' => true,
        'message' => 'Warning! Custom SKU. Cannot deduct.',
    ]);
});
