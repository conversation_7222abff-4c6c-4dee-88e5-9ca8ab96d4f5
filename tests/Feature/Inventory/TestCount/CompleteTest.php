<?php

use App\Models\Box;
use App\Models\Employee;
use App\Models\Location;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/complete-test-count';
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
});

test('complete test count - missing location_id', function () {
    $this->params = [
        'location_id' => '',
        'employee_id' => $this->employee->code,
        'id_time_checking' => 123,
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'location_id' => ['The location id field is required.']
    ]);
});
test('complete test count - missing employee_id', function () {
    $this->params = [
        'location_id' => $this->location->id,
        'employee_id' => '',
        'id_time_checking' => 123,
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'employee_id' => ['The employee id field is required.']
    ]);
});
test('complete test count - The selected employee id is invalid.', function () {
    $employee = Employee::factory()->create(['warehouse_id' => 2, 'code' => 2022]);
    $this->params = [
        'location_id' => $this->location->id,
        'employee_id' => $employee->code,
        'id_time_checking' => 123,
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'employee_id' => ['The selected employee id is invalid.']
    ]);
});
test('complete test count - SUCCESS.', function () {
    $box_found = [];
    $box_moving = [];
    $locationOther = Location::factory()->create(
        [
            'type' => Location::RACK,
            'warehouse_id' => $this->warehouse->id,
            'barcode' => 'LO456'
        ]);
    for ($i = 0; $i < 6; $i++) {
        if ($i % 2 == 0) {
            $box = Box::factory()->create([
                'location_id' => $this->location->id,
                'barcode' => 'barcode' . $i,
                'is_deleted' => 0,
                'warehouse_id' => $this->warehouse->id
            ]);
            $box_found[] = $box->barcode;
        } else {
            $box = Box::factory()->create([
                'location_id' => $locationOther->id,
                'barcode' => 'barcode' . $i,
                'is_deleted' => 0,
                'warehouse_id' => $this->warehouse->id
            ]);
            $box_moving[] = $box->barcode;
        }
    }
    $this->params = [
        'location_id' => $this->location->id,
        'employee_id' => $this->employee->code,
        'id_time_checking' => 123,
        'barcode_found' => $box_found,
        'barcode_moving' => $box_moving,
        'barcode_not_found' => [],
        'barcode_new' => [
            [
                'barcode' => 'test count haha111',
                'gtin' => '00051054112898',
                'quantity' => 24,
                'price_per_item' => 1,
                'cost_value' => 24,
                'is_input_valid' => true,
                'is_input_quantity_valid' => true,
                'is_input_price_valid' => true,
                'product_id' => 11111
            ]
        ],
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(201)->assertJsonStructure(
        [
            'id',
            'box_available',
            'box_on_hand',
            'warehouse_id',
            'location_id',
            'user_id',
            'note',
            'employee_id',
            'created_at',
            'updated_at'
        ]);
    $responseData = json_decode($response->getContent());
    $this->assertDatabaseHas('test_count', ['id' => $responseData->id]);
});
test('complete test count - Error - mexico - validate country.', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    $box_found = [];
    $box_moving = [];
    $location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $warehouse->id, 'barcode' => 'LO123']);
    $locationOther = Location::factory()->create(
        [
            'type' => Location::RACK,
            'warehouse_id' => $warehouse->id,
            'barcode' => 'LO456'
        ]);
    for ($i = 0; $i < 6; $i++) {
        if ($i % 2 == 0) {
            $box = Box::factory()->create([
                'location_id' => $this->location->id,
                'barcode' => 'barcode' . $i,
                'is_deleted' => 0,
                'warehouse_id' => $this->warehouse->id
            ]);
            $box_found[] = $box->barcode;
        } else {
            $box = Box::factory()->create([
                'location_id' => $locationOther->id,
                'barcode' => 'barcode' . $i,
                'is_deleted' => 0,
                'warehouse_id' => $this->warehouse->id
            ]);
            $box_moving[] = $box->barcode;
        }
    }
    $this->params = [
        'location_id' => $location->id,
        'employee_id' => $employee->code,
        'id_time_checking' => 123,
        'barcode_found' => $box_found,
        'barcode_moving' => $box_moving,
        'barcode_not_found' => [],
        'barcode_new' => [
            [
                'barcode' => 'test-count-new-1',
                'gtin' => '00051054112898',
                'quantity' => 24,
                'price_per_item' => 1,
                'cost_value' => 24,
                'is_input_valid' => true,
                'is_input_quantity_valid' => true,
                'is_input_price_valid' => true,
                'product_id' => 9898
            ]
        ],
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => ['Missing country for box: test-count-new-1']
    ]);
});
test('complete test count - SUCCESS - mexico.', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    $box_found = [];
    $box_moving = [];
    $location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $warehouse->id, 'barcode' => 'ABCXYZ']);
    $locationOther = Location::factory()->create(
        [
            'type' => Location::RACK,
            'warehouse_id' => $warehouse->id,
            'barcode' => 'LO456'
        ]);
    for ($i = 0; $i < 6; $i++) {
        if ($i % 2 == 0) {
            $box = Box::factory()->create([
                'location_id' => $location->id,
                'barcode' => 'barcode' . $i,
                'is_deleted' => 0,
                'warehouse_id' => $warehouse->id
            ]);
            $box_found[] = $box->barcode;
        } else {
            $box = Box::factory()->create([
                'location_id' => $locationOther->id,
                'barcode' => 'barcode' . $i,
                'is_deleted' => 0,
                'warehouse_id' => $warehouse->id
            ]);
            $box_moving[] = $box->barcode;
        }
    }
    $this->params = [
        'location_id' => $location->id,
        'employee_id' => $employee->code,
        'id_time_checking' => 123,
        'barcode_found' => $box_found,
        'barcode_moving' => $box_moving,
        'barcode_not_found' => [],
        'barcode_new' => [
            [
                'barcode' => 'test-count-new-1',
                'gtin' => '00051054112898',
                'quantity' => 24,
                'price_per_item' => 1,
                'cost_value' => 24,
                'is_input_valid' => true,
                'is_input_quantity_valid' => true,
                'is_input_price_valid' => true,
                'country_save' => 'VN',
                'product_id' => 9898
            ]
        ],
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(201)->assertJsonStructure(
        [
            'id',
            'box_available',
            'box_on_hand',
            'warehouse_id',
            'location_id',
            'user_id',
            'note',
            'employee_id',
            'created_at',
            'updated_at'
        ]);
    $responseData = json_decode($response->getContent());
    $this->assertDatabaseHas('test_count', ['id' => $responseData->id]);
});
