<?php

use App\Models\Client;
use App\Models\PaymentAccount;
use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Stripe\StripeClient;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/api/seller/stripe/payment-method';
    DB::table('payment_accounts')->insert([
        'provider' => 'stripe',
        'api_key' => 'fake_api_key',
        'api_secret' => 'sk_test_51QEkXkD50wemo9v8sbap1Wn2aCms7FGOPEuoyAoogQwTPQCPViY3vOEdnbkTk0QMQl9av4YkJwSi7v2cGHCXCujj00elN4mKgo',
        'webhook_secret' => 'fake_webhook_secret',
    ]);
    $this->account = PaymentAccount::where('provider', 'stripe')->orderByDesc('id')->first();

    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $this->stripeClient = new StripeClient([
        'api_key' => $this->account->api_secret
    ]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);
    foreach (TeamMemberRolePermission::listFuction() as $function) {
        $permissions[] = [
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ];
        TeamMemberRolePermission::create([
            'team_member_role_id' => $this->role->id,
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ]);
    }
    $this->permissions = $permissions;
    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456', 'root_username' => $this->client->username]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->withHeaders([
        'Authorization' => "Bearer $this->token",
        'Accept' => 'application/json',
    ]);
});

test('Member has no permission', function () {
    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::WALLET_INVOICE_FUNCTION
    ])->update([
        'permission' => TeamMemberRolePermission::NO_PERMISSION,
    ]);
    $response = $this->post($this->endpoint, [
        'payment_method_id' => 'pm_card_visa'
    ]);
    $response->assertStatus(403);
});

test('It add card with success', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_visa'
    ]);
    $response->assertStatus(200);
    $paymentMethods = $this->get($this->endpoint);
    $paymentMethods->assertStatus(200);
    $paymentMethods->assertJsonFragment([
        'id' => $response->json('id'),
        'is_default' => false,
    ]);
});

test('It add card with success and is default', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_mastercard',
        'is_default' => true
    ]);

    $response->assertStatus(200);

    $paymentMethods = $this->get($this->endpoint);

    $paymentMethods->assertStatus(200);
    $paymentMethods->assertJsonFragment([
        'id' => $response->json('id'),
        'is_default' => true
    ]);
});

test('It add card with generic decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_chargeDeclined'
    ]);
    $response->assertStatus(500);
    $message = $response->json('message');
    $this->assertEquals('Your card was declined.', $message);
});

test('It add card with insufficient funds decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_visa_chargeDeclinedInsufficientFunds'
    ]);
    $response->assertStatus(500);
    $this->assertEquals('Your card has insufficient funds.', $response->json('message'));
});

test('It add card with lost card decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_visa_chargeDeclinedLostCard'
    ]);
    $response->assertStatus(500);
    $this->assertEquals('Your card was declined.', $response->json('message'));
});

test('It add card with stolen card decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_visa_chargeDeclinedStolenCard'
    ]);
    $response->assertStatus(500);
    $this->assertEquals('Your card was declined.', $response->json('message'));
});

test('It add card with expired card decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_chargeDeclinedExpiredCard'
    ]);
    $response->assertStatus(500);
    $this->assertEquals('Your card has expired.', $response->json('message'));
});

test('It add card with incorrect cvc decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_chargeDeclinedIncorrectCvc'
    ]);

    $response->assertStatus(500);
    $this->assertEquals("Your card's security code is incorrect.", $response->json('message'));
});

test('It add card with processing error decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_chargeDeclinedProcessingError'
    ]);
    $response->assertStatus(500);
    $this->assertEquals('An error occurred while processing your card. Try again in a little bit.', $response->json('message'));
});

test('It add card with exceeding velocity limit decline', function () {
    $response = $this->postJson($this->endpoint, [
        'payment_method_id' => 'pm_card_visa_chargeDeclinedVelocityLimitExceeded'
    ]);
    $response->assertStatus(500);
    $this->assertEquals('Your card was declined for making repeated attempts too frequently or exceeding its amount limit.', $response->json('message'));
});

afterEach(function () {
    $this->store = Store::where('id', $this->store->id)->first();

    if ($this->store->stripe_id) {
        $this->stripeClient->customers->delete($this->store->stripe_id);
    }

    PaymentAccount::where('id', $this->account->id)->delete();
});
