<?php

namespace Tests\Feature\QuickBook;

use App\Jobs\QuickBook\CreateCustomerJob;
use App\Models\QuickBookCompany;
use App\Models\QuickBookSyncHistory;
use App\Models\Store;
use App\Services\QuickBook\CustomerService;
use App\Services\QuickBookService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use QuickBooksOnline\API\Data\IPPCustomer;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->company = QuickBookCompany::factory()->create();
});

test('Create customer not exists in QBO', function () {
    $store = Store::factory()->create();
    
    $customerServiceMock = Mockery::mock(CustomerService::class);
    $qboCustomer = Mockery::mock(IPPCustomer::class);
    $qboCustomer->Id = 'CUS-12345';

    $customerServiceMock->shouldReceive('createCustomer')
        ->once()
        ->andReturn($qboCustomer);

    $quickBookServiceMock = Mockery::mock(QuickBookService::class)->makePartial();
    $quickBookServiceMock
        ->shouldReceive('getService')
        ->once()
        ->with($this->company->company_id, QuickBookCompany::ENTITY_TYPE_CUSTOMER)
        ->andReturn($customerServiceMock);

    $this->app->instance(QuickBookService::class, $quickBookServiceMock);

    $job = new CreateCustomerJob($this->company->company_id, $store->id);
    $job->handle($this->app->make(QuickBookService::class));

    $history = QuickBookSyncHistory::query()
        ->where('company_id', $this->company->company_id)
        ->where('spa_id', $store->id)
        ->where('qb_ref_type', QuickBookCompany::ENTITY_TYPE_CUSTOMER)
        ->latest('id')
        ->first();

    expect($history)->not->toBeNull();
    expect($history->status)->toBe(QuickBookSyncHistory::STATUS_SUCCESS);

    // check QuickBook Mapping
    $mapping = $store->quickBookCustomers($this->company->company_id)->first();
    expect($mapping)->not->toBeNull();
    expect($mapping->qb_ref_id)->toBe($qboCustomer->Id);
    expect($mapping->qb_ref_type)->toBe(QuickBookCompany::ENTITY_TYPE_CUSTOMER);
    expect($mapping->spa_id)->toBe($store->id);
    expect($mapping->spa_model)->toBe(Store::class);
    expect($mapping->company_id)->toBe($this->company->company_id);
    expect($mapping->action)->toBe(QuickBookSyncHistory::ACTION_CREATE);
});

// test('Create customer exists in QBO', function () {
//     //
// });
