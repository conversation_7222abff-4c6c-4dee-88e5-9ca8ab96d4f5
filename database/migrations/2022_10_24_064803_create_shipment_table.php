<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShipmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipment', function (Blueprint $table) {
            $table->id();
            $table->integer('warehouse_id')->nullable()->default('0');
            $table->integer('account_id')->nullable()->default('0');
            $table->integer('store_id')->nullable()->default('0');
            $table->integer('order_id')->nullable();
            $table->enum('shipment_account', ['swiftpod', 'store', 'amazon', 'swiftpod_store'])->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->date('ship_date')->nullable();
            $table->decimal('shipment_cost', 13, 2)->nullable();
            $table->decimal('insurance_cost', 13, 2)->nullable();
            $table->string('carrier_code')->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('tracking_status')->nullable();
            $table->string('service_code')->nullable();
            $table->tinyInteger('is_return_label')->nullable();
            $table->string('package_code')->nullable();
            $table->decimal('weight_value', 13, 2)->nullable();
            $table->enum('weight_unit', ['pounds', 'ounces', 'grams'])->nullable();
            $table->decimal('dimension_length', 13, 2)->nullable();
            $table->decimal('dimension_width', 13, 2)->nullable();
            $table->decimal('dimension_height', 13, 2)->nullable();
            $table->enum('dimension_unit', ['inches', 'centimeters'])->nullable();
            $table->integer('shipment_quantity')->default('0');
            $table->string('label_url', 500)->nullable();
            $table->string('label_zpl_url', 500)->nullable();
            $table->enum('confirmation', ['online', 'delivery', 'signature', 'adult_signature', 'verbal'])->nullable();
            $table->enum('insurance', ['none', 'shipsurance', 'carrier', 'external'])->nullable();
            $table->integer('employee_create_id')->nullable();
            $table->enum('refund_status', ['submitted', 'refunded', 'rejected', 'deactive'])->nullable();
            $table->integer('employee_refund_id')->nullable();
            $table->integer('sync_tracking')->default('0');
            $table->tinyInteger('sync_tracking_retry')->default('0');
            $table->enum('provider', ['shipstation', 'easypost', 'manual'])->default('shipstation');
            $table->tinyInteger('is_auto_created')->default('0');
            $table->string('url_tracking_easypost', 500)->nullable();
            $table->tinyInteger('is_deleted')->default('0');
            $table->integer('employee_printed_id')->nullable();
            $table->tinyInteger('is_manual')->default(0);
            $table->string('account_shipping_easypost')->nullable();
            $table->string('usps_zone')->nullable();
            $table->timestamp('est_delivery_at')->nullable();
            $table->decimal('shipx_rate', 13, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipment');
    }
}
