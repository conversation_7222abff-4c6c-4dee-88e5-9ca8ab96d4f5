<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoreSettingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('store_setting', function (Blueprint $table) {
            $table->id();
            $table->integer('store_id'); // Khóa ngoại tới bảng store
            $table->string('key')->nullable();                  // Tên cài đặt
            $table->string('value')->nullable();      // Giá trị cài đặt
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('store_setting');
    }
}
