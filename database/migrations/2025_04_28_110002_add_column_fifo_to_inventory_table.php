<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnFifoToInventoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inventory', function (Blueprint $table) {
            $table->bigInteger('replicated_from_id')->nullable();
            $table->bigInteger('fifo_calculated_at')->default(\App\Models\Inventory::FIFO_NOT_CALCULATED);
            $table->integer('remaining_qty')->default(0);
            $table->integer('stock_qty_ending')->default(0);
            $table->double('stock_value_ending')->default(0);
            $table->double('cost_total')->default(0);

            $table->index(['fifo_calculated_at']);
            $table->index(['remaining_qty']);
            $table->index(['product_id'], 'product_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
