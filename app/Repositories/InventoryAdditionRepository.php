<?php

namespace App\Repositories;

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Country;
use App\Models\Inventory;
use App\Models\InventoryAddition;
use App\Models\InventoryReference;
use App\Models\Location;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderBox;
use App\Models\PurchaseOrderItem;
use App\Models\Setting;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryAdditionRepository
{
    const LIMIT = 10;

    public function fetchAll($input = null)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = DB::table('inventory_addition')
            ->select('inventory_addition.*', 'user.username as username',
                'location.barcode as location', 'product.name as product_name', 'product.sku as product_sku', 'box.barcode as box_id', 'vendor.name as vendor', 'employee.name')
            ->join('user', 'user.id', '=', 'inventory_addition.user_id')
            ->leftJoin('vendor', 'vendor.id', '=', 'inventory_addition.vendor_id')
            ->leftJoin('employee', 'employee.code', '=', 'inventory_addition.employee_id')
            ->join('product', 'product.id', '=', 'inventory_addition.product_id')
            ->join('location', 'location.id', '=', 'inventory_addition.location_id')
            ->leftJoin('box', 'box.id', '=', 'inventory_addition.box_id')
            ->leftJoin('purchase_order', 'purchase_order.id', '=', 'inventory_addition.po_id');

        if (!empty($input['location'])) {
            $keyword = escapeLike($input['location']);
            $query->where('location.barcode', 'LIKE', '%' . $keyword . '%');
        }

        if (!empty($input['product'])) {
            $keyword = escapeLike($input['product']);
            $query->where('product.name', 'LIKE', '%' . $keyword . '%');
        }

        if (!empty($input['box'])) {
            $keyword = escapeLike($input['box']);
            $query->where('box.barcode', 'LIKE', '%' . $keyword . '%');
        }

        if (!empty($input['po_number'])) {
            $keyword = escapeLike($input['po_number']);
            $query->where('purchase_order.po_number', 'LIKE', '%' . $keyword . '%');
        }

        if (!empty($input['invoice_number'])) {
            $keyword = escapeLike($input['invoice_number']);
            $query->where('purchase_order.invoice_number', 'LIKE', '%' . $keyword . '%');
        }

        if (!empty($input['tracking_number'])) {
            $keyword = escapeLike($input['tracking_number']);
            $query->where('inventory_addition.tracking_number', 'LIKE', '%' . $keyword . '%');
        }

        if (!empty($input['sku'])) {
            $keyword = escapeLike($input['sku']);
            $query->where('product.sku', 'like', '%' . $keyword . '%');
        }

        if (!empty($input['date'])) {
            $data = $input['date'];

            if (!empty($data[0])) {
                $query->whereDate('inventory_addition.created_at', '>=', $data[0]);
            }

            if (!empty($data[1])) {
                $query->whereDate('inventory_addition.created_at', '<=', $data[1]);
            }
        }

        $data = $query->where('inventory_addition.warehouse_id', $input['warehouse_id'])
            ->orderBy('inventory_addition.id', 'desc')
            ->paginate($limit);
        $countryIso2 = [];
        $objectIds = [];

        foreach ($data->items() as $item) {
            if (!empty($item->country)) {
                $countryIso2[] = $item->country;
            }

            $objectIds = array_merge($objectIds, [$item->id]);
        }

        if (!empty($countryIso2)) {
            $iso2 = array_unique($countryIso2);
            $iso2 = Country::select('id', 'name', 'iso2')
                ->whereIn('iso2', $iso2)
                ->get();
            $iso2 = $iso2->keyBy('iso2');
        }

        $inventoryReferenced = InventoryReference::query()
            ->select('inventory.object_id')
            ->join('inventory', 'inventory.id', '=', 'inventory_references.referenced_inventory_id')
            ->where('inventory.object_name', Inventory::OBJECT_ADDITION)
            ->whereIn('inventory.object_id', $objectIds)
            ->groupBy('inventory.object_id')
            ->get()
            ->keyBy('object_id');

        foreach ($data->items() as &$item) {
            if (!empty($item->country) && isset($iso2[$item->country])) {
                $item->country_name = $iso2[$item->country]?->name;
            } else {
                $item->country_name = '';
            }

            $item->is_referenced = $inventoryReferenced->has($item->id);
        }

        return $data;
    }

    public function create($input)
    {
        $validate = $this->validateBoxLocation($input);

        if (!empty($validate)) {
            return response()->json($validate, 422);
        }

        if ($input['quantity'] <= 0) {
            return response()->json([
                'product' => ['Quantity must be greater than zero']
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        DB::beginTransaction();

        try {
            $purchaseOrder = PurchaseOrder::where('id', $input['po_id'])->where('warehouse_id', config('jwt.warehouse_id'))->lockForUpdate()->first();

            if (!$purchaseOrder) {
                DB::rollBack();

                return response()->json([
                    'purchase_order' => ['Not found purchase order']
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            if ($purchaseOrder->order_status == PurchaseOrder::COMPLETED_STATUS || $purchaseOrder->order_status == PurchaseOrder::CANCELLED_STATUS) {
                DB::rollBack();

                return response()->json([
                    'purchase_order' => ['The purchase order has been completed or canceled and cannot be updated!']
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $poItemExisted = PurchaseOrderItem::where('po_id', $input['po_id'])
                ->where('product_id', $input['product_id'])
                ->lockForUpdate()
                ->first();

            if (!$poItemExisted) {
                DB::rollBack();

                return response()->json(['product' => ['This product does not exist in the order']], 422);
            }

            if ($input['quantity'] > ($poItemExisted->quantity - $poItemExisted->quantity_onhand)) {
                DB::rollBack();

                return response()->json(['product' => ['The quantity is exceeded the one in purchase order. Please re-enter the correct number.']], 422);
            }

            if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                $dataPartNumber = PartNumber::where('country', $input['country'])->where('product_id', $input['product_id'])->pluck('id')->first();

                if (!$dataPartNumber) {
                    DB::rollBack();

                    return response()->json(['partNumber' => ['There is no part number available for both the country and the product.']], 422);
                }
            }

            $input['country'] = !empty($input['country']) && in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO) ? $input['country'] : null;
            ///Todo : build + update data BOX, PO
            $input = $this->buildData($input);
            $inventoryAddition = InventoryAddition::create($input);
            ///Todo : insert Inventory
            $this->insertBoxMoving($input, $inventoryAddition->id);
            //Todo : insert Box Moving
            //Todo : insert location product
            LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], $input['quantity']);
            $receivedQuantity = $input['quantity'] + $poItemExisted->quantity_onhand;
            $isFullyReceived = $receivedQuantity == $poItemExisted->quantity;
            $poItemExisted->received_status = $isFullyReceived ? PurchaseOrderItem::RECEIVED_STATUS : PurchaseOrderItem::PARTIAL_RECEIVED_STATUS;
            $poItemExisted->quantity_onhand = $receivedQuantity;
            $poItemExisted->save();
            $isPartialReceivedPurchaseOrder = PurchaseOrderItem::where('po_id', $purchaseOrder->id)
                ->where(function ($query) {
                    $query->where('received_status', PurchaseOrderItem::PARTIAL_RECEIVED_STATUS)
                        ->orWhereNull('received_status');
                })
                ->exists();
            $purchaseOrder->order_status = $isPartialReceivedPurchaseOrder ? PurchaseOrder::PARTIAL_RECEIVED_STATUS : PurchaseOrder::COMPLETED_STATUS;
            $purchaseOrder->save();

            ///Todo : update  Product Quantity
            ProductQuantityRepository::updateQuantityAndIncomeQuantity($input['warehouse_id'], $input['product_id'], $input['quantity'],
                -$input['quantity']);
            $inventoryAddition->cost_value = $input['quantity'] * $poItemExisted->price;
            $inventoryAddition->save();
            $input['cost_total'] = $inventoryAddition->cost_value;
            $this->insertInventory($input, $inventoryAddition->id);

            if (!empty($input['box_id'])) {
                $box = Box::find($input['box_id']);
                $box->cost_value = $inventoryAddition->cost_value;
                $box->po_id = $purchaseOrder->id;
                $box->po_item_id = $poItemExisted->id;
                $box->save();
            }

            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

            DB::commit();

            return response()->json(InventoryAddition::with('country:name,iso2')->latest('id')->first(), 201);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InventoryAdditionRepository.create', [
                'input' => $input,
                'exception' => $exception,
            ]);

            throw $exception;
        }
    }

    private function validateBoxLocation($input)
    {
        ////Validate barcode unique ( box_id)
        if (!empty($input['barcode'])) {
            $box = Box::where('barcode', $input['barcode'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->whereNotNull('location_id')
                ->first();

            if ($box) {
                $message['barcode'] = ['The box id has already been taken.'];

                return $message;
            }

            $locationIsRack = Location::where('id', $input['location_id'])
                ->where('type', 0)->count();
            $locationIsDarkPod = Location::where('id', $input['location_id'])
                ->where('type', Location::MOVING_SHELVES)->where('warehouse_id', 1)->count();

            if (!$locationIsRack && !$locationIsDarkPod) {
                $message['location_id'] = ['Location must be rack or valid dark pod'];

                return $message;
            }
        ///Todo : Validate accept box hasn't product
        } else {
            $locationIsPullingShelve = Location::where('id', $input['location_id'])
                ->where('type', 1)
                ->count();
            $locationIsDarkPod = Location::where('id', $input['location_id'])
                ->where('type', Location::MOVING_SHELVES)->where('warehouse_id', 1)->count();

            if (!$locationIsPullingShelve && !$locationIsDarkPod) {
                $message['location_id'] = ['Please enter Box ID or select a valid pulling shelve/dark pod location.'];

                return $message;
            }
        }

        $errorMessageLocationMaxBox = $this->checkLocationMaxBox($input['location_id']);

        if ($errorMessageLocationMaxBox) {
            return ['location_id' => [$errorMessageLocationMaxBox]];
        }

        return [];
    }

    private function insertBoxMoving($input, $inventoryAddId)
    {
        $inputBoxMoving['box_id'] = !empty($input['box_id']) ? $input['box_id'] : null;
        $inputBoxMoving['location_id'] = $input['location_id'];
        $inputBoxMoving['warehouse_id'] = $input['warehouse_id'];
        $inputBoxMoving['user_id'] = $input['user_id'];
        $inputBoxMoving['product_id'] = $input['product_id'];
        $inputBoxMoving['quantity'] = $input['quantity'];
        $inputBoxMoving['inventory_addition_id'] = $inventoryAddId;

        return BoxMoving::create($inputBoxMoving);
    }

    private function insertInventory($input, $inventoryAddId = -1)
    {
        $inputInventory['direction'] = $input['direction'] ?? Inventory::DIRECTION_INPUT;
        $inputInventory['type'] = $input['type'] ?? Inventory::TYPE_INPUT;
        $inputInventory['product_id'] = $input['product_id'];
        $inputInventory['warehouse_id'] = $input['warehouse_id'];
        $inputInventory['location_id'] = $input['location_id'];
        $inputInventory['user_id'] = $input['user_id'];
        $inputInventory['object_id'] = $inventoryAddId;
        $inputInventory['object_name'] = $input['object_name'] ?? Inventory::OBJECT_ADDITION;
        $inputInventory['quantity'] = $input['quantity'];
        $inputInventory['cost_total'] = $input['cost_total'] ?? 0;

        return Inventory::create($inputInventory);
    }

    private function buildData(&$data)
    {
        ///Todo : create Purchase Order
        $data = $this->buildDataPo($data);
        ///Todo : create Box
        $data = $this->buildDataBox($data);

        return $data;
    }

    private function buildDataBox(&$data)
    {
        if (!empty($data['barcode'])) {
            $box_existed = Box::where('warehouse_id', $data['warehouse_id'])
                ->where('barcode', $data['barcode'])
                ->first();

            if (!empty($box_existed)) {
                Box::where('id', $box_existed->id)->update([
                    'product_id' => $data['product_id'],
                    'quantity' => $data['quantity'],
                    'location_id' => $data['location_id'],
                    'country' => $data['warehouse_id'] == Warehouse::WAREHOUSE_MEXICO_ID ? $data['country'] : null,
                ]);
                $data['box_id'] = $box_existed->id;
            } else {
                $inputBox['barcode'] = $data['barcode'];
                $inputBox['warehouse_id'] = $data['warehouse_id'];
                $inputBox['product_id'] = $data['product_id'];
                $inputBox['quantity'] = $data['quantity'];
                $inputBox['location_id'] = !empty($data['location_id']) ? $data['location_id'] : null;
                $inputBox['country'] = $data['warehouse_id'] == Warehouse::WAREHOUSE_MEXICO_ID ? $data['country'] : null;
                $box = Box::create($inputBox);
                $data['box_id'] = $box->id;
            }
        }

        return $data;
    }

    private function buildDataPo(&$data)
    {
        if (!empty($data['po_id'])) {
            $order_exited = PurchaseOrder::where('id', $data['po_id'])->first();

            if (empty($order_exited)) {
                $this->autoCreatePo($data);
            } else {
                $data['invoice_number'] = $order_exited->invoice_number;
            }
        } else {
            $this->autoCreatePo($data);
        }

        return $data;
    }

    private function autoCreatePo(&$data)
    {
        $order = PurchaseOrder::create([
            'po_number' => $data['po_number'],
            'warehouse_id' => $data['warehouse_id'],
            'order_status' => PurchaseOrder::INCOMPLETED_STATUS,
            'user_id' => $data['user_id'],
            'invoice_number' => !empty($data['invoice_number']) ? $data['invoice_number'] : null
        ])->fresh();
        $data['po_id'] = $order->id;
        $data['invoice_number'] = $order->invoice_number;
    }

    public function fetch($id)
    {
        return InventoryAddition::find($id);
    }

    public function update($id, $dataUpdate)
    {
        return InventoryAddition::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        return InventoryAddition::where('id', $id)->delete();
    }

    public function revertInventoryAddition($id)
    {
        $inventoryAddition = InventoryAddition::where('id', $id)
            ->first()
            ->toArray();
        $purchaseOrder = PurchaseOrder::where('id', $inventoryAddition['po_id'])
            ->first();

        if ($purchaseOrder->order_status === PurchaseOrder::CANCELLED_STATUS) {
            return response()->json([
                'status' => false,
                'message' => ['The purchase order has been canceled!']
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $box = Box::where('id', $inventoryAddition['box_id'])
            ->where('location_id', $inventoryAddition['location_id'])
            ->active()
            ->first();

        if (!$box) {
            return response()->json([
                'status' => false,
                'message' => 'The box could not be found in this inventory addition!'
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        DB::beginTransaction();

        try {
            $lockInventory = Inventory::query()
                ->join('inventory_references', 'inventory_references.referenced_inventory_id', '=', 'inventory.id')
                ->where('inventory.object_id', $id)
                ->where('inventory.object_name', Inventory::OBJECT_ADDITION)
                ->lockForUpdate()
                ->first();

            if (!empty($lockInventory)) {
                DB::rollBack();

                return response()->json([
                    'status' => false,
                    'message' => 'Cannot revert – this addition has been used in FIFO cost calculation.'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $box->is_deleted = Box::DELETED;
            $box->save();

            InventoryAddition::query()
                ->where('id', $id)
                ->update(['is_deleted' => 1]);
            Inventory::query()
                ->where('object_id', $id)
                ->where('object_name', Inventory::OBJECT_ADDITION)
                ->update([
                    'remaining_qty' => 0,
                    'is_deleted' => 1,
                ]);
            BoxMoving::query()
                ->where('inventory_addition_id', $id)
                ->update(['is_deleted' => 1]);

            $inventoryAdditionRevert = Inventory::query()
                ->where('object_id', $id)
                ->where('object_name', Inventory::OBJECT_ADDITION)
                ->orderBy('created_at')
                ->orderBy('id')
                ->get();

            foreach ($inventoryAdditionRevert as $item) {
                $newInventory = $item->replicate();
                $newInventory->direction = Inventory::DIRECTION_OUTPUT;
                $newInventory->type = Inventory::TYPE_REVERT;
                $newInventory->object_name = Inventory::OBJECT_REVERT_ADDITION;
                $newInventory->fifo_calculated_at = Inventory::FIFO_NOT_CALCULATED;
                $newInventory->stock_qty_ending = 0;
                $newInventory->stock_value_ending = 0;
                $newInventory->remaining_qty = 0;
                $newInventory->created_at = now('UTC');
                $newInventory->user_id = auth()->id() ?? $item->user_id;
                $newInventory->replicated_from_id = $item->id;
                $newInventory->is_deleted = 1;
                $newInventory->save();

                InventoryReference::query()->create([
                    'inventory_id' => $newInventory->id,
                    'referenced_inventory_id' => $item->id,
                    'quantity' => $item->quantity,
                    'cost_total' => $item->cost_total,
                ]);
            }

            $orderItem = PurchaseOrderItem::where('po_id', $inventoryAddition['po_id'])
                ->where('new_item', 0)
                ->get()->toArray();

            if (!empty($orderItem)) {
                foreach ($orderItem as $item) {
                    if ($item['product_id'] == $inventoryAddition['product_id']) {
                        $quantityOnhand = $item['quantity_onhand'] - $inventoryAddition['quantity'];
                        $receivedStatus = $quantityOnhand >= $item['quantity'] ? PurchaseOrderItem::RECEIVED_STATUS : PurchaseOrderItem::PARTIAL_RECEIVED_STATUS;
                        PurchaseOrderItem::where('id', $item['id'])
                            ->update([
                                'received_status' => $receivedStatus,
                                'quantity_onhand' => $quantityOnhand,
                            ]);
                    }
                }

                $totalItemReceived = PurchaseOrderItem::where('po_id', $inventoryAddition['po_id'])
                    ->where('received_status', PurchaseOrderItem::RECEIVED_STATUS)
                    ->where('new_item', 0)
                    ->count();
                $orderStatus = count($orderItem) == $totalItemReceived ? PurchaseOrder::COMPLETED_STATUS : PurchaseOrder::PARTIAL_RECEIVED_STATUS;
                $purchaseOrder->order_status = $orderStatus;
                $purchaseOrder->save();
            }
            ///Todo : order có new_item
            $orderItem = PurchaseOrderItem::where('po_id', $inventoryAddition['po_id'])->where('new_item', 1)->get();
            $orderItem = $orderItem->toArray();

            if (!empty($orderItem)) {
                foreach ($orderItem as $item) {
                    if ($item['product_id'] == $inventoryAddition['product_id']) {
                        $quantityOnhand = $item['quantity_onhand'] - $inventoryAddition['quantity'];

                        if ($quantityOnhand == 0) {
                            PurchaseOrderItem::where('id', $item['id'])->delete();
                        } else {
                            PurchaseOrderItem::where('id', $item['id'])
                                ->update(['quantity_onhand' => $quantityOnhand]);
                        }
                    }
                }
            }

            ///Todo : update Location Product
            LocationProductRepository::updateQuantity($inventoryAddition['location_id'], $inventoryAddition['product_id'], $inventoryAddition['quantity'] * -1);
            ///Todo : update  Product Quantity
            ProductQuantityRepository::updateQuantityAndIncomeQuantity($inventoryAddition['warehouse_id'], $inventoryAddition['product_id'],
                $inventoryAddition['quantity'] * -1, $inventoryAddition['quantity']);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InventoryAdditionRepository.revertInventoryAddition', [
                'id' => $id,
                'exception' => $exception
            ]);

            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function createInventoryAdditionDistributor($input)
    {
        $validate = $this->validateLocation($input);

        if (!empty($validate)) {
            return response()->json($validate, 422);
        }

        $existTracking = InventoryAddition::where('tracking_number', $input['tracking_number'])
            ->where('is_deleted', 0)
            ->where('warehouse_id', $input['warehouse_id'])
            ->first();

        if (!empty($existTracking)) {
            return response()->json(['tracking_number' => ['This tracking number was already added.']], 422);
        }

        DB::beginTransaction();

        try {
            $purchaseOrder = PurchaseOrder::where('po_number', $input['po_number'])
                ->where('warehouse_id', config('jwt.warehouse_id'))
                ->where('invoice_number', $input['invoice_number'])
                ->lockForUpdate()
                ->first();

            if (empty($purchaseOrder)) {
                DB::rollBack();

                return response()->json(['purchase_order' => ['Not found purchase order']], 422);
            }

            $input['po_id'] = $purchaseOrder->id;

            if ($purchaseOrder->order_status == PurchaseOrder::COMPLETED_STATUS || $purchaseOrder->order_status == PurchaseOrder::CANCELLED_STATUS) {
                DB::rollBack();

                return response()->json([
                    'purchase_order' => ['The purchase order has been completed or canceled and cannot be updated!']
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $poItems = PurchaseOrderItem::where('po_id', $input['po_id'])->lockForUpdate()->get();
            $dataPartNumber = [];

            if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                $dataPartNumber = PartNumber::where('country', $input['country'])->pluck('id', 'product_id');

                if ($dataPartNumber->isEmpty()) {
                    DB::rollBack();

                    return response()->json(['partNumber' => ['There is no part number available for both the country and the product']], 422);
                }
            }

            $additionIds = [];

            foreach ($input['items'] as $itemInput) {
                if ($itemInput['quantity'] <= 0) {
                    DB::rollBack();

                    return response()->json([
                        'product' => ["Quantity of product {$itemInput['sku']} must be greater than zero"]
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }

                $product = Product::where('sku', $itemInput['sku'])->first();

                if (!$product) {
                    DB::rollBack();

                    return response()->json(['product' => ['product in not found']], 404);
                }

                $matchedOrderItem = $poItems->firstWhere('product_id', $product->id);

                if (!$matchedOrderItem) {
                    DB::rollBack();

                    return response()->json(['product' => ['product not found in purchase order']], 422);
                }

                if ($itemInput['quantity'] > ($matchedOrderItem->quantity - $matchedOrderItem->quantity_onhand)) {
                    DB::rollBack();

                    return response()->json(['product' => ['The quantity exceeds the one in the purchase order. Please re-enter the correct number.']], 422);
                }

                if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                    // check gtin doi voi warehouse mexico neu khong co gtin thi se khong cho tao inventory
                    if (empty($itemInput['gtin'])) {
                        DB::rollBack();

                        return response()->json(['product' => ['The product sku ' . $itemInput['sku'] . ' does not have a gtin.']], 422);
                    }

                    //check xem san pham co map voi part number hay khong
                    if (!isset($dataPartNumber[$itemInput['product_id']])) {
                        DB::rollBack();

                        return response()->json(['product' => ['The product sku ' . $itemInput['sku'] . ' is not mapped with a part number.']], 422);
                    }
                }

                $locationId = $itemInput['location_id'] ?? $input['location_id'] ?? null;
                ProductQuantityRepository::updateQuantityAndIncomeQuantity($input['warehouse_id'], $product->id,
                    $itemInput['quantity'], -$itemInput['quantity']);

                if (!empty($input['barcode'])) {
                    $boxData = [
                        'barcode' => $input['barcode'],
                        'warehouse_id' => $input['warehouse_id'],
                        'product_id' => $product->id,
                        'quantity' => $itemInput['quantity'],
                        'location_id' => $input['location_id'] ?? null,
                        'country' => in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO) ? $input['country'] : null,
                    ];
                    $box = Box::create($boxData);
                    $input['box_id'] = $box->id;
                }

                $inventoryAdditionData = array_merge($input, [
                    'product_id' => $product->id,
                    'quantity' => $itemInput['quantity'],
                    'location_id' => $locationId,
                    'gtin' => $itemInput['gtin'],
                    'country' => in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO) ? $input['country'] : null,
                ]);
                $inventoryAddition = InventoryAddition::create($inventoryAdditionData);
                $additionIds[] = $inventoryAddition->id;
                BoxMoving::create([
                    'box_id' => $box->id ?? null,
                    'pre_location_id' => null,
                    'location_id' => $locationId,
                    'warehouse_id' => $input['warehouse_id'],
                    'user_id' => $input['user_id'] ?? null,
                    'product_id' => $product->id,
                    'inventory_addition_id' => $inventoryAddition->id,
                    'quantity' => $itemInput['quantity'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                LocationProductRepository::updateQuantity($locationId, $product->id, $itemInput['quantity']);
                $quantityOnHand = $matchedOrderItem->quantity_onhand + $itemInput['quantity'];
                $matchedOrderItem->quantity_onhand = $quantityOnHand;
                $matchedOrderItem->received_status = $quantityOnHand == $matchedOrderItem->quantity ? PurchaseOrderItem::RECEIVED_STATUS : PurchaseOrderItem::PARTIAL_RECEIVED_STATUS;
                $matchedOrderItem->save();
                $inventoryAddition->cost_value = $matchedOrderItem->price * $itemInput['quantity'];
                $inventoryAddition->save();
                $inventoryData = [
                    'direction' => Inventory::DIRECTION_INPUT,
                    'type' => Inventory::TYPE_INPUT,
                    'product_id' => $product->id,
                    'warehouse_id' => $input['warehouse_id'],
                    'location_id' => $locationId,
                    'box_id' => $box->id ?? null,
                    'user_id' => $input['user_id'],
                    'object_id' => $inventoryAddition->id,
                    'object_name' => Inventory::OBJECT_ADDITION,
                    'quantity' => $itemInput['quantity'],
                    'cost_total' => $inventoryAddition->cost_value,
                ];
                Inventory::create($inventoryData);

                if (isset($box)) {
                    $box->cost_value = $inventoryAddition->cost_value;
                    $box->po_id = $matchedOrderItem->po_id;
                    $box->po_item_id = $matchedOrderItem->id;
                    $box->save();
                }
            }

            PurchaseOrderBox::where('tracking_number', $input['tracking_number'])
                ->where('po_id', $input['po_id'])
                ->update([
                    'received_at' => Carbon::now()->toDateTimeString()
                ]);
            $isPartialReceivedPurchaseOrder = PurchaseOrderItem::where('po_id', $purchaseOrder->id)
                ->where(function ($query) {
                    $query->where('received_status', PurchaseOrderItem::PARTIAL_RECEIVED_STATUS)
                        ->orWhereNull('received_status');
                })
                ->exists();
            $purchaseOrder->order_status = $isPartialReceivedPurchaseOrder ? PurchaseOrder::PARTIAL_RECEIVED_STATUS : PurchaseOrder::COMPLETED_STATUS;
            $purchaseOrder->save();
            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InventoryAdditionRepository.createInventoryAdditionDistributor', [
                'input' => $input,
                'exception' => $exception,
            ]);

            throw $exception;
        }

        return response()->json(InventoryAddition::with('country:name,iso2')->whereIn('id', $additionIds)->get(), 201);
    }

    private function validateLocation(&$input)
    {
        $items = $input['items'];

        if (isset($input['location_type'])) {
            // if pulling shelves
            if ($input['location_type'] == 1) {
                $location = Location::where('warehouse_id', $input['warehouse_id'])
                    ->where('type', '1')
                    ->first();
                $input['location_id'] = $location->id;
            }

            if ($input['location_type'] == 0) {
                if (empty($input['barcode'])) {
                    $message['barcode'] = ['Box ID is required'];

                    return $message;
                }
                $input['location_name'] = trim($input['location_name']);

                if (empty($input['location_name'])) {
                    $message['location'] = ['Location is required'];

                    return $message;
                }

                $location = Location::where('warehouse_id', $input['warehouse_id'])
                    ->where('type', 0)
                    ->where('barcode', $input['location_name'])
                    ->where('is_deleted', 0)
                    ->first();

                if (!$location) {
                    $message['location'] = ['Location not found'];

                    return $message;
                }

                $input['location_id'] = $location->id;
            }
        }

        if (!empty($input['barcode'])) {
            $box = Box::where('barcode', $input['barcode'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->whereNotNull('location_id')
                ->first();

            if ($box) {
                $message['barcode'] = ['The box id has already been taken.'];

                return $message;
            }

            $locationIsRack = Location::where('id', $input['location_id'])
                ->where('type', 0)->count();

            if (!$locationIsRack) {
                $message['location_id'] = ['Location must is rack'];

                return $message;
            }
        }

        // if multi item
        if (count($items) > 1) {
            foreach ($items as $itemInput) {
                if (empty($itemInput['sku'])) {
                    return ['product' => ['Product SKU is required']];
                }

                if (empty($itemInput['quantity']) || $itemInput['quantity'] <= 0) {
                    return ['product' => ['Quantity must be greater than zero']];
                }

                if (empty($itemInput['location_id'])) {
                    return ['location_id' => ['Location is required']];
                }
                $locationType = $itemInput['location_type'] ?? 1;
                $warehouseId = $locationType == Location::MOVING_SHELVES ? 1 : $input['warehouse_id']; //darkpod only apply for SJ
                $location = Location::where('id', $itemInput['location_id'])
                    ->where('warehouse_id', $warehouseId)
                    ->where('type', $locationType)
                    ->first();
                if (!$location) {
                    return ['location_id' => ['Invalid location for SKU ' . $itemInput['sku']]];
                }
            }
            $errorMessageLocationMaxBox = $this->checkLocationMaxBox($itemInput['location_id']);

            if ($errorMessageLocationMaxBox) {
                return ['location_id' => [$errorMessageLocationMaxBox]];
            }
        } else {
            $errorMessageLocationMaxBox = $this->checkLocationMaxBox($input['location_id']);

            if ($errorMessageLocationMaxBox) {
                return ['location_id' => [$errorMessageLocationMaxBox]];
            }
        }

        return [];
    }

    public function manualAddition($input)
    {
        DB::transaction(function () use ($input) {
            ///Todo : build + update data BOX, PO
            $input = $this->buildDataBox($input);

            $InventoryAddition = InventoryAddition::create($input);
            ///Todo : insert Inventory
            $this->insertBoxMoving($input, $InventoryAddition->id);
            //Todo : insert Box Moving
            $this->insertInventory($input, $InventoryAddition->id);
            //Todo : insert location product
            LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], $input['quantity']);
            ///Todo : update  Product Quantity
            ProductQuantityRepository::updateQuantityAndIncomeQuantity($input['warehouse_id'], $input['product_id'],
                $input['quantity'], $input['quantity'] * -1);
        });
    }

    private function checkLocationMaxBox($locationId)
    {
        $location = Location::where('id', $locationId)->first();

        if ($location->type != Location::RACK) {
            return null;
        }

        $settingLocationMaxBox = Setting::where('name', Setting::LOCATION_MAX_BOX)->first();
        $locationMaxBox = !is_null($settingLocationMaxBox) ? $settingLocationMaxBox->value : 100;
        $currentCount = Box::where('location_id', $locationId)
            ->where('is_deleted', Box::NOT_DELETED)
            ->lockForUpdate()
            ->count();

        if ($currentCount >= $locationMaxBox) {
            return "This location reached maximum $locationMaxBox boxes. Choose another location.";
        }

        return null;
    }
}
