<?php

namespace App\Repositories;

use App\Http\Service\PrintAreaService;
use App\Models\BarcodeStatus;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class QualityControlRepository
{
    protected $printAreaSevice;

    public function __construct()
    {
        $this->printAreaSevice = new PrintAreaService();
    }

    public function isItemSaleOrderItemBarcode($input)
    {
        $dataSelect = [
            'sale_order_item_barcode.id',
            'sale_order_item_barcode.order_item_id',
            'sale_order_item_barcode.sku',
            'sale_order_item.options',
            'sale_order_item.print_side',
            'product.id as product_id',
            'product.name',
            'sale_order_item_barcode.label_id',
            'sale_order_item_barcode.order_id',
            'sale_order_item_barcode.staged_at',
            'sale_order.is_fba_order',
            'sale_order_item_barcode.part_number_id',
            'sale_order_item.product_color_sku',
            'sale_order_item.ink_color',
            'sale_order.is_test',
            'sale_order.is_fba_order',
            'sale_order.order_type',
            'sale_order.is_manual',
            'sale_order.is_xqc',
            'sale_order.is_eps',
            'sale_order.is_create_manual',
            'sale_order.tag',
            'sale_order.internal_note',
        ];

        $data = DB::table('sale_order_item_barcode')
            ->select($dataSelect)
            ->join('sale_order_item', 'sale_order_item.sku', '=', 'sale_order_item_barcode.sku')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->where('sale_order_item_barcode.label_id', $input['label'])
            ->where('sale_order_item_barcode.is_deleted', '=', 0)
            ->first();

        if (!$data) {
            $data = DB::table('sale_order_item_barcode')
                ->select($dataSelect)
                ->join('sale_order_item', 'sale_order_item.sku', '=', 'sale_order_item_barcode.sku')
                ->join('product', 'product.id', '=', 'sale_order_item.product_id')
                ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
                ->where('sale_order_item_barcode.sku', $input['label'])
                ->where('sale_order_item_barcode.is_deleted', '=', 0)
                ->first();
        }

        if (!$data) {
            return null;
        }

        $sku = $data->sku;

        $data->order_date = DB::table('sale_order_item_image')
            ->where('sku', $sku)
            ->pluck('order_date')
            ->first();

        $imageColorBySide = DB::table('sale_order_item_image')
            ->where('sku', $sku)
            ->get(['is_purple', 'print_side']);

        $sides = [];
        if (!$imageColorBySide->isEmpty()) {
            foreach ($imageColorBySide as $image) {
                $productPrintSide = ProductPrintSide::findByCode($image->print_side);
                if ($productPrintSide) {
                    $sides[$productPrintSide->code_name] = $image->is_purple;
                }
            }
        }
        $data = $this->getImageOption($data, $sku, $data->order_date, $sides);

        $storeName = DB::table('sale_order')
            ->select('store.name', 'sale_order.order_folding_status', 'store.code')
            ->join('store', 'store.id', '=', 'sale_order.store_id')
            ->where('sale_order.id', $data->order_id)
            ->first();
        $orderInsert = DB::table('sale_order_insert')
            ->where('order_id', $data->order_id)
            ->get();

        if ($data->part_number_id) {
            $countryPartNumber = PartNumber::where('id', $data->part_number_id)->first()?->country ?? null;
        }
        $data->country_part_number = $countryPartNumber ?? null;
        $data->store_name = $storeName ? $storeName->name : '';
        $data->store_code = $storeName ? $storeName->code : null;
        $data->order_insert = $orderInsert ?? null;
        $data->order_pulled_status = $storeName->order_folding_status;

        //product color
        $productColor = ProductColor::where('sku', '=', $data->product_color_sku)->first();
        $data->color_code = $productColor->color_code ?? null;
        $data->order_types = $this->generateOrderType($data) ?? null;
        $name = $this->convertNameProductCanvas($data->product_id);
        $data->name = $data->name . ' ' . $name;

        return $data;
    }

    public function convertNameProductCanvas($productId)
    {
        $product = Product::with('productStyle')
            ->whereHas('productStyle', function ($query) {
                $query->where('type', 'Canvas');
            })
            ->find($productId);

        if ($product && $product->description) {
            $descriptionArray = explode(' ', $product->description);
            $lastValue = $descriptionArray[count($descriptionArray) - 1];

            if (is_numeric($lastValue)) {
                return '/ '. $lastValue;
            }
        }

        return null;
    }

    public function generateOrderType($data)
    {
        $orderType = [];

        if ($data->is_test) {
            $orderType[] = 'Order Test';
        }
        if ($data->is_xqc) {
            $orderType[] = 'XQC';
        }
        if ($data->is_fba_order) {
            $orderType[] = 'FBA';
        }
        if ($data->is_manual) {
            $orderType[] = 'Manual Process';
        }
        if ($data->is_create_manual) {
            $orderType[] = 'Manual Order';
        }
        if ($data->order_type == SaleOrder::ORDER_TYPE_PRETREATED) {
            $orderType[] = 'Pretreated Order';
        } elseif ($data->order_type == SaleOrder::ORDER_TYPE_BLANK) {
            $orderType[] = 'Blank Order';
        } elseif ($data->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER) {
            $orderType[] = 'Label Order';
        } elseif ($data->order_type == SaleOrder::ORDER_TYPE_LICENSE_ORDER) {
            $orderType[] = 'Licensed Order';
        }

        return $orderType;
    }

    public function getImageOption($data, $sku = null, $orderDate = null, $sides = [])
    {
        $dataOptions = json_decode($data->options);
        $data->map_options = $this->printAreaSevice->mapOptionSaleOrderItem($dataOptions, $sku, $orderDate, $sides);

        return $data;
    }

    public function updateBarcodeStatus($label_id, $status = 'pass')
    {
        $updateData = [
            'status' => $status,
            'last_qc_at' => Carbon::now(),
        ];

        if ($status != 'pass') {
            $updateData['total_reject'] = DB::raw('total_reject + 1');
        }

        return BarcodeStatus::updateOrCreate(['label_id' => $label_id], $updateData);
    }
}
