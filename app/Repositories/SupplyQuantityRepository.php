<?php

namespace App\Repositories;

use App\Models\SupplyQuantity;

class SupplyQuantityRepository
{
    static function updateQuantity($warehouseId, $supplyId, $quantity = 0)
    {
        $supplyQuantity = SupplyQuantity::where('supply_id', $supplyId)
            ->where('warehouse_id', $warehouseId)
            ->first();
        if (!empty($supplyQuantity)) {
            SupplyQuantity::where('id', $supplyQuantity->id)->update(['quantity' => $supplyQuantity->quantity + $quantity]);
            return;
        }

        SupplyQuantity::create([
            'warehouse_id' => $warehouseId,
            'supply_id' => $supplyId,
            'quantity' => $quantity,
        ]);
    }
}
