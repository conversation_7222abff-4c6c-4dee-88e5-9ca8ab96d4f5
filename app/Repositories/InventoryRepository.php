<?php

namespace App\Repositories;

use App\Models\Inventory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class InventoryRepository
{
    const LIMIT = 10;

    public function fetchDetail($input)
    {
        $page = !empty($input['page']) ? $input['page'] : 1;
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $offset = ($page - 1) * $limit;
        $bindings = [];
        $query = $this->buildQuery($input);
        $query .= ' GROUP BY product.id';

        $query_select = 'SELECT brand.name AS brand_name,
                        product.style,
                        product.size ,
                        product.color,
                        product.sku,
                        product.gtin,
                        product.id,' . $query;

        $query_select = "SELECT *, (begining + input - output) AS ending  FROM ($query_select)AS tb";
        $query_total = "SELECT COUNT(*) AS total FROM ($query_select)AS tb";

        if (!empty($input['ending'])) {
            $bindings[] = $input['ending'];
            $query_select .= ' WHERE (begining + INPUT - OUTPUT) < ?';
            $query_total .= ' WHERE (begining + INPUT - OUTPUT) < ?';
        }

        if (!empty($input['sort_column'])) {
            $sortColumn = in_array($input['sort_column'], ['ending']) ? $input['sort_column'] : 'ending';
            $sortBy = !empty($input['sort_by']) && in_array(strtolower($input['sort_by']), ['asc', 'desc']) ? $input['sort_by'] : 'asc';
            $query_select .= " order by $sortColumn $sortBy";
            $query_total .= " order by $sortColumn $sortBy";
        }

        $query_select .= ' LIMIT ? OFFSET ?';

        $data = DB::select($query_select, array_merge($bindings, [$limit, $offset]));
        $total = DB::select($query_total, $bindings);
        $total = $total[0]->total;

        return new LengthAwarePaginator(
            $data,
            $total,
            $limit,
            $page,
        );
    }

    public function fetchTotalDetail($input)
    {
        $query = 'SELECT';
        $query .= $this->buildQuery($input);

        $query .= ' GROUP BY product.id';

        $query = "SELECT
                SUM(begining) AS begining,
                SUM(input) AS input,
                SUM(OUTPUT) AS output,
                SUM(begining + INPUT - OUTPUT) AS ending FROM ($query)AS tb";

        $query = $this->buildConditionSort($input, $query);
        $data = DB::select($query);

        return $data[0];
    }

    private function buildConditionSort($input, $query)
    {
        if (!empty($input['ending'])) {
            $ending = $input['ending'];
            $query .= " WHERE (begining + INPUT - OUTPUT) < '$ending'";
        }

        if (!empty($input['sort_column'])) {
            $sortColumn = in_array($input['sort_column'], ['ending']) ? $input['sort_column'] : 'ending';
            $sortBy = !empty($input['sort_by']) && in_array(strtolower($input['sort_by']), ['asc', 'desc']) ? $input['sort_by'] : 'asc';
            $query .= " order by $sortColumn $sortBy";
        }

        return $query;
    }

    private function buildQuery($input)
    {
        if (empty($input['date']) || count($input['date']) < 2) {
            $start_date = Carbon::now()->subDays(30)->toDateString();
            $end_date = Carbon::now()->toDateString();
        } else {
            $data = $input['date'];
            $start_date = $data[0];
            $end_date = $data[1];
        }

        $warehouse_id = $input['warehouse_id'];

        $query = " SUM(CASE
                               WHEN direction = 0

                                    AND inventory.created_at < '" . addslashes($start_date) . "' THEN inventory.quantity
                               ELSE 0
                           END) -

                          SUM(CASE
                               WHEN direction = 1

                                    AND inventory.created_at < '" . addslashes($start_date) . "' THEN inventory.quantity
                               ELSE 0
                           END)

                        AS begining,

                       SUM(CASE
                               WHEN direction=0
                                    AND inventory.created_at >= '" . addslashes($start_date) . "'
                                    AND inventory.created_at <= '" . addslashes($end_date) . "' THEN inventory.quantity
                               ELSE 0
                           END) AS input,
                       SUM(CASE
                               WHEN direction=1
                                    AND inventory.created_at >= '" . addslashes($start_date) . "'
                                    AND inventory.created_at<= '" . addslashes($end_date) . "' THEN inventory.quantity
                               ELSE 0
                           END) AS output

                FROM inventory
                JOIN product ON inventory.product_id = product.id JOIN brand ON product.brand_id = brand.id";

        $query .= " WHERE inventory.is_deleted = 0 AND inventory.warehouse_id = '" . addslashes($warehouse_id) . "'";

        if (!empty($input['brand_id'])) {
            $brandId = $input['brand_id'];
            $query .= " AND brand.id = '" . addslashes($brandId) . "'";
        }
        if (!empty($input['sku'])) {
            $sku = $input['sku'];
            $query .= " AND product.sku = '" . addslashes($sku) . "'";
        }

        if (!empty($input['style']) && !empty($input['gtin'])) {
            $style = $input['style'];
            $gtin = $input['gtin'];
            $query .= " AND (product.style = '" . addslashes($style) . "' OR product.gtin = '" . addslashes($gtin) . "')";
        } elseif (!empty($input['style'])) {
            $style = $input['style'];
            $query .= " AND product.style = '" . addslashes($style) . "'";
        } elseif (!empty($input['gtin'])) {
            $gtin = $input['gtin'];
            $query .= " AND product.gtin = '" . addslashes($gtin) . "'";
        }

        return $query;
    }

    public function buildQueryInventoryOverview($input)
    {
        $warehouseId = $input['warehouse_id'];
        $year = now()->startOfQuarter()->subQuarter()->year;
        $quarter = now()->startOfQuarter()->subQuarter()->quarter;
        $yearNow = Carbon::now('America/Los_Angeles')->year;
        $query = " FROM
            (SELECT
                product.id AS product_id,
                product.sku,
                product.style,
                product.size,
                product.color,
                product.gtin,
                product.gtin_case,
                tmp_product_box.quantity_product,
                tmp_product_box.box_total_cost_value,
                tmp_product_box.total_box,
                tmp_product_box.box_quantiy
            FROM
                (
                    (SELECT
                        location_product.product_id,
                        location_product.quantity_product,
                        box.total_box,
                        box.box_total_cost_value,
                        box.box_quantiy
                    FROM
                        (SELECT
                            SUM(location_product.quantity) quantity_product,
                                location_product.product_id
                        FROM
                            location_product
                        JOIN location ON location_product.location_id = location.id
                            AND location.type = 1 AND location.warehouse_id = '" . addslashes($warehouseId) . "' ";

        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }

        $query .= " GROUP BY location_product.product_id) AS location_product
                        LEFT JOIN
                    (SELECT
                            SUM(box.quantity) box_quantiy,
                            COUNT(box.id) AS total_box,
                            SUM(box.cost_value) as box_total_cost_value,
                            box.product_id
                    FROM
                        box
                    JOIN location ON location.id = box.location_id
                        AND location.type = 0
                        AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        AND box.is_deleted = 0
                        AND box.product_id IS NOT NULL";

        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }
        $query .= " GROUP BY box.product_id) AS box ON box.product_id = location_product.product_id
                UNION SELECT
                    box.product_id,
                    location_product.quantity_product,
                    box.total_box,
                    box.box_total_cost_value,
                    box.box_quantiy

                FROM
                    (SELECT
                        SUM(location_product.quantity) quantity_product,
                            location_product.product_id
                    FROM
                        location_product
                    JOIN location ON location_product.location_id = location.id
                        AND location.type = 1 AND location.warehouse_id = '" . addslashes($warehouseId) . "'";
        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }

        $query .= " GROUP BY location_product.product_id) AS location_product
                        RIGHT JOIN
                    (SELECT
                        SUM(box.quantity) box_quantiy,
                            COUNT(box.id) AS total_box,
                            SUM(box.cost_value) AS box_total_cost_value,
                            box.product_id
                    FROM
                        box
                    JOIN location ON location.id = box.location_id
                        AND location.type = 0
                        AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        AND box.is_deleted = 0
                        AND box.product_id IS NOT NULL";
        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }
        $query .= " GROUP BY box.product_id) AS box  ON box.product_id = location_product.product_id) AS tmp_product_box
                    RIGHT JOIN product
                        ON product.id = tmp_product_box.product_id) WHERE product.parent_id != 0 AND product.is_deleted = 0) AS tb
                    LEFT JOIN(
                        SELECT `po_id`,
                            `product_id`,
                            `price`,
                            `order_date`
                        FROM
                            (
                            SELECT
                                `po_id`,
                                `product_id`,
                                `price`,
                                purchase_order.order_date,
                                ROW_NUMBER() OVER(
                                PARTITION BY `product_id`
                            ORDER BY
                                purchase_order.order_date
                            DESC
                                ,
                                po_id ASC
                            ) AS rn
                        FROM
                            `purchase_order_item`
                        INNER JOIN `purchase_order` ON `purchase_order`.`id` = `purchase_order_item`.`po_id`
                        WHERE
                            `price` IS NOT NULL AND price != 0 AND `purchase_order`.`warehouse_id` = '" . addslashes($warehouseId) . "' AND `purchase_order`.`order_status` != 'cancelled') AS ranked_orders
                            WHERE
                                rn = 1
                        ) AS latest_po_price
                    ON tb.product_id = latest_po_price.product_id
                    LEFT JOIN (
                        SELECT
                            location_product.product_id,
                            SUM(location_product.quantity) AS quantity_product
                        FROM location_product
                        JOIN location
                            ON location_product.location_id = location.id
                        WHERE location.type = 3
                            AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        GROUP BY location_product.product_id
                    ) AS moving_product_box
                    ON tb.product_id = moving_product_box.product_id
                    LEFT JOIN product_rank_histories
                        ON (product_rank_histories.product_id = tb.product_id
                            AND product_rank_histories.year = {$year}
                            AND product_rank_histories.quarter = {$quarter})
                    LEFT JOIN (
                        SELECT product.id,
                            IFNULL(SUM(CASE WHEN purchase_order.id IS NOT NULL THEN purchase_order_item.price ELSE 0 END), 0) AS total_price,
                            COUNT(purchase_order.id) AS total_po,
                            product_blank_cost_year.cost,
                            product_blank_cost_year.end_unit
                        FROM product
                        LEFT JOIN purchase_order_item
                            ON purchase_order_item.product_id = product.id
                        LEFT JOIN purchase_order
                            ON purchase_order.id = purchase_order_item.po_id
                            AND purchase_order.order_status IN ('completed', 'partial_received')
                            AND YEAR(purchase_order.order_date) = " . $yearNow . '
                        LEFT JOIN product_blank_cost_year
                            ON product_blank_cost_year.product_id = product.id
                        WHERE product_blank_cost_year.year = ' . ($yearNow - 1) . "
                        GROUP BY product.id
                    ) AS blank_cost_year ON blank_cost_year.id = tb.product_id
                    LEFT JOIN product_quantity
                        ON tb.product_id = product_quantity.product_id
                        AND product_quantity.warehouse_id = '" . addslashes($warehouseId) . "'
                    WHERE 1";

        if (!empty($input['rank'])) {
            $rank = addslashes($input['rank']);

            if ($rank == '-') {
                $query .= ' AND product_rank_histories.id is null';
            } else {
                $query .= " AND product_rank_histories.rank='{$rank}'";
            }
        }

        if (!empty($input['style'])) {
            $style = $input['style'];
            $query .= " AND tb.style LIKE '%" . addslashes(addslashes($style)) . "%'";
        }
        if (!empty($input['styles'])) {
            $styles = $input['styles'];
            $stylesArray = explode(',', $styles);
            $quotedStylesArray = array_map(function ($style) {
                return "'" . trim($style) . "'";
            }, $stylesArray);
            $stylesList = implode(', ', $quotedStylesArray);
            $query .= ' AND tb.style IN ' . '(' . $stylesList . ')';
        }
        if (!empty($input['sku'])) {
            $sku = $input['sku'];
            $query .= " AND tb.sku LIKE '%$sku%'";
        }
        if (!empty($input['gtin'])) {
            $gtin = $input['gtin'];
            $query .= " AND tb.gtin LIKE '%$gtin%'";
        }
        if (!empty($input['out_stock_pulling_quantity'])) {
            $query .= ' AND((IFNULL(tb.quantity_product, 0) / COALESCE(tb.gtin_case, 1)) * 100) < 25';
        }
        if (!empty($input['out_stock_moving_quantity'])) {
            $query .= ' AND((IFNULL(moving_product_box.quantity_product, 0) / COALESCE(tb.gtin_case, 1)) * 100) < 25';
        }
        if (!empty($input['out_stock_rack_quantity'])) {
            $query .= ' AND (tb.total_box < 5 OR tb.total_box IS NULL)';
        }

        $query .= ' GROUP BY tb.`product_id`';

        return $query;
    }

    public function fetchInventoryOverview($input)
    {
        $query = $this->buildQueryInventoryOverview($input);
        $query_total = 'SELECT COUNT(*)' . $query;

        $total = count(DB::select($query_total));

        $page = !empty($input['page']) ? $input['page'] : 1;
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $offset = ($page - 1) * $limit;

        if (!empty($input['sort_column'])) {
            $sortColumn = in_array($input['sort_column'], ['pulling_quantity', 'rack_quantity', 'box_total_cost_value', 'pulling_shelves_cost_value', 'moving_quantity', 'moving_cost_value'])
                ? $input['sort_column']
                : 'pulling_quantity';
            $sortBy = !empty($input['sort_by']) && in_array(strtolower($input['sort_by']), ['asc', 'desc']) ? $input['sort_by'] : 'asc';
            $query .= " ORDER BY $sortColumn $sortBy";
        }

        $query_select = 'SELECT
                tb.product_id,
                tb.sku,
                tb.style,
                tb.size,
                tb.color,
                tb.gtin,
                COALESCE(moving_product_box.quantity_product, 0) as moving_quantity,
                COALESCE(latest_po_price.price, 0) * COALESCE(moving_product_box.quantity_product, 0) AS moving_cost_value,
                COALESCE(tb.total_box, 0) AS total_box,
                COALESCE(tb.box_quantiy, 0) AS rack_quantity,
                COALESCE(tb.box_total_cost_value, 0) AS box_total_cost_value,
                COALESCE(tb.quantity_product, 0) AS pulling_quantity,
                COALESCE(latest_po_price.price, 0) * COALESCE(tb.quantity_product, 0) AS pulling_shelves_cost_value,
                COALESCE(((COALESCE(tb.quantity_product, 0) / COALESCE(tb.gtin_case, 1)) * 100), 0) AS percent_quantity,
                COALESCE(((COALESCE(moving_product_box.quantity_product, 0) / COALESCE(tb.gtin_case, 1)) * 100), 0) AS moving_percent_quantity,
                COALESCE(SUM(product_quantity.`incoming_stock`), 0) AS incoming_stock,
                COALESCE(product_rank_histories.rank, "-") AS product_rank,
                blank_cost_year.total_price,
                blank_cost_year.total_po,
                blank_cost_year.cost AS blank_cost_last_year,
                blank_cost_year.end_unit';

        $query .= ' LIMIT ? OFFSET ?';
        $query_select .= $query;
        $data = DB::select($query_select, [$limit, $offset]);

        return new LengthAwarePaginator(
            $data,
            $total,
            $limit,
            $page,
        );
    }

    public function fetchTotalOverview($input)
    {
        $warehouseId = $input['warehouse_id'];

        $query = "SELECT SUM(td.total_box) AS total_box,
                  SUM(td.rack_quantity) AS total_rack,
                  SUM(td.box_cost_value) AS total_box_cost_value,
                  SUM(td.pulling_shelves_cost_value) AS total_pulling_shelves_cost_value,
                  SUM(td.box_cost_value) + SUM(td.pulling_shelves_cost_value) AS total_warehouse_cost_value,
                  SUM(td.moving_shelves_quantity_product) AS total_moving,
                  SUM(td.moving_shelves_cost_value) AS total_moving_shelves_cost_value,
                  SUM(td.box_cost_value) + SUM(td.pulling_shelves_cost_value) AS total_warehouse_cost_value,
                  SUM(td.pulling_quantity) AS total_pulling FROM (SELECT
                 tb.total_box,
                 tb.box_quantiy AS rack_quantity,
                 tb.quantity_product AS pulling_quantity,
                 tb.latest_po_price * tb.quantity_product AS pulling_shelves_cost_value,
                 tb.box_cost_value,
                 tb.latest_po_price * tb.moving_shelves_quantity_product AS moving_shelves_cost_value,
                 tb.moving_shelves_quantity_product
                FROM
                (SELECT
                    moving_shelves.quantity_product as moving_shelves_quantity_product,
                    location_product.product_id,
                    location_product.quantity_product,
                    COALESCE(latest_po_price.price, 0) AS latest_po_price,
                    box.total_box,
                    box.box_quantiy,
                    box.box_cost_value
                FROM
                    (SELECT
                        SUM(location_product.quantity) quantity_product,
                            location_product.product_id
                    FROM
                        location_product
                    JOIN location ON location_product.location_id = location.id
                        AND location.type = 1 AND location.warehouse_id = '" . addslashes($warehouseId) . "' ";

        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }

        $query .= " GROUP BY location_product.product_id) AS location_product
                    LEFT JOIN(
                        SELECT location_product.product_id,
                                SUM(location_product.quantity) AS quantity_product
                        FROM
                                location_product
                        JOIN location ON location_product.location_id = location.id
                        WHERE
                                location.type = 3 AND location.warehouse_id = '1'
                        GROUP BY
                                location_product.product_id
                            ) AS moving_shelves
                        ON
                            location_product.product_id = moving_shelves.product_id
                    LEFT JOIN (
                        SELECT
                            `po_id`,
                            `product_id`,
                            `price`,
                            `order_date`
                        FROM (
                            SELECT
                                `po_id`,
                                `product_id`,
                                `price`,
                                purchase_order.order_date,
                                ROW_NUMBER() OVER (PARTITION BY `product_id` ORDER BY purchase_order.order_date DESC, po_id ASC) AS rn
                            FROM
                                `purchase_order_item`
                            INNER JOIN `purchase_order` ON `purchase_order`.`id` = `purchase_order_item`.`po_id`
                            WHERE
                                `price` IS NOT NULL
                                AND price != 0
                                AND `purchase_order`.`warehouse_id` = '" . addslashes($warehouseId) . "'
                                AND `purchase_order`.`order_status` != 'cancelled'
                        ) AS ranked_orders
                        WHERE rn = 1
                    ) AS latest_po_price ON latest_po_price.product_id = location_product.product_id

                        LEFT JOIN
                    (SELECT
                            SUM(box.quantity) box_quantiy,
                            COUNT(box.id) AS total_box,
                            SUM(box.cost_value) AS box_cost_value,
                            box.product_id
                    FROM
                        box
                    JOIN location ON location.id = box.location_id
                        AND location.type = 0
                        AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        AND box.is_deleted = 0
                        AND box.product_id IS NOT NULL";

        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }
        $query .= " GROUP BY box.product_id) AS box ON box.product_id = location_product.product_id
                UNION SELECT
                    moving_shelves.quantity_product as moving_shelves_quantity_product,
                    box.product_id,
                    location_product.quantity_product,
                    COALESCE(latest_po_price.price, 0) AS latest_po_price,
                    box.total_box,
                    box.box_quantiy,
                    box.box_cost_value

                FROM
                    (SELECT
                        SUM(location_product.quantity) quantity_product,
                            location_product.product_id
                    FROM
                        location_product
                    JOIN location ON location_product.location_id = location.id
                        AND location.type = 1 AND location.warehouse_id = '" . addslashes($warehouseId) . "'";
        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }

        $query .= " GROUP BY location_product.product_id) AS location_product
                    LEFT JOIN(
                        SELECT location_product.product_id,
                                SUM(location_product.quantity) AS quantity_product
                        FROM
                                location_product
                        JOIN location ON location_product.location_id = location.id
                        WHERE
                                location.type = 3 AND location.warehouse_id = '1'
                        GROUP BY
                                location_product.product_id
                            ) AS moving_shelves
                        ON
                            location_product.product_id = moving_shelves.product_id

                    LEFT JOIN (
                        SELECT
                            `po_id`,
                            `product_id`,
                            `price`,
                            `order_date`
                        FROM (
                            SELECT
                                `po_id`,
                                `product_id`,
                                `price`,
                                purchase_order.order_date,
                                ROW_NUMBER() OVER (PARTITION BY `product_id` ORDER BY purchase_order.order_date DESC, po_id ASC) AS rn
                            FROM
                                `purchase_order_item`
                            INNER JOIN `purchase_order` ON `purchase_order`.`id` = `purchase_order_item`.`po_id`
                            WHERE
                                `price` IS NOT NULL
                                AND price != 0
                                AND `purchase_order`.`warehouse_id` = '" . addslashes($warehouseId) . "'
                                AND `purchase_order`.`order_status` != 'cancelled'
                        ) AS ranked_orders
                        WHERE rn = 1
                    ) AS latest_po_price ON latest_po_price.product_id = location_product.product_id

                        RIGHT JOIN
                    (SELECT
                        SUM(box.quantity) box_quantiy,
                            COUNT(box.id) AS total_box,
                            SUM(box.cost_value) AS box_cost_value,
                            box.product_id
                    FROM
                        box
                    JOIN location ON location.id = box.location_id
                        AND location.type = 0
                        AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        AND box.is_deleted = 0
                        AND box.product_id IS NOT NULL";
        if (!empty($input['location'])) {
            $location = $input['location'];
            $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
        }

        $year = now()->startOfQuarter()->subQuarter()->year;
        $quarter = now()->startOfQuarter()->subQuarter()->quarter;
        $query .= " GROUP BY box.product_id) AS box  ON box.product_id = location_product.product_id) AS tb
                    RIGHT JOIN product_quantity ON tb.product_id = product_quantity.`product_id` JOIN product ON product_quantity.product_id = product.`id`
                    LEFT JOIN product_rank_histories
                        ON (product_rank_histories.product_id = tb.product_id
                            AND product_rank_histories.year = {$year}
                            AND product_rank_histories.quarter = {$quarter})
                 WHERE product_quantity.`warehouse_id` = '" . addslashes($warehouseId) . "'";

        if (!empty($input['rank'])) {
            $rank = addslashes($input['rank']);

            if ($rank == '-') {
                $query .= ' AND product_rank_histories.id is null';
            } else {
                $query .= " AND product_rank_histories.rank='{$rank}'";
            }
        }

        if (!empty($input['style'])) {
            $style = $input['style'];
            $query .= " AND product.style LIKE '%" . addslashes(addslashes($style)) . "%'";
        }
        if (!empty($input['sku'])) {
            $sku = $input['sku'];
            $query .= " AND product.sku LIKE '%" . addslashes(addslashes($sku)) . "%'";
        }
        if (!empty($input['gtin'])) {
            $gtin = $input['gtin'];
            $query .= " AND product.gtin LIKE '%" . addslashes(addslashes($gtin)) . "%'";
        }
        if (!empty($input['out_stock_pulling_quantity'])) {
            $query .= ' AND((tb.quantity_product / product.gtin_case) * 100) < 25';
        }
        if (!empty($input['out_stock_moving_quantity'])) {
            $query .= ' AND((tb.moving_shelves_quantity_product / product.gtin_case) * 100) < 25';
        }
        if (!empty($input['out_stock_rack_quantity'])) {
            $query .= ' AND (tb.total_box < 5 OR tb.total_box IS NULL)';
        }
        $query .= ') AS td';
        $data = DB::select($query);

        return $data[0];
    }

    public function buildQueryInventoryOverviewForExport($input)
    {
        $warehouseId = $input['warehouse_id'];

        $query = "SELECT product.sku,SUM(IFNULL(product_quantity.quantity, 0)) AS current_stock,
                SUM(product_quantity.`incoming_stock`) AS incoming_stock,
                IF(unshipping_table.`unshipping` is null , '0', unshipping_table.`unshipping` ) as unshipping
                FROM product
                LEFT JOIN product_quantity ON product.id = product_quantity.product_id
                LEFT JOIN (SELECT sale_order_item.product_id , COUNT(*) unshipping FROM sale_order_item_barcode
                JOIN sale_order_item ON sale_order_item_barcode.order_item_id = sale_order_item.id
                JOIN sale_order on sale_order.id = sale_order_item.order_id
                WHERE sale_order_item_barcode.is_deleted  = 0
                AND sale_order.warehouse_id = '" . addslashes($warehouseId) . "'
                AND sale_order_item_barcode.created_at >= '2022-04-12'
                AND sale_order_item_barcode.pulled_at IS null
                AND sale_order_item_barcode.employee_pull_id IS null
                GROUP BY sale_order_item.product_id HAVING sale_order_item.product_id IS NOT null) as unshipping_table
 				ON unshipping_table.product_id = product.id
         WHERE product_quantity.warehouse_id= '" . addslashes($warehouseId) . "' GROUP BY product.id";

        return $query;
    }

    public function addInventory(Inventory $invetory)
    {
        if ($invetory->warehouse_id) {
            return false;
        }
        /*  if () {

        }*/
    }

    public function fetchInventoryEndUnit($startDate, $endDate, $warehouseId)
    {
        $sql = 'SELECT * FROM (SELECT product.sku,
                  product.id as product_id,
                  product.style as product_style,
                  SUM(CASE

                          WHEN direction = 0 AND inventory.created_at < ? THEN inventory.quantity

                          ELSE 0

                      END) - SUM(CASE

                                     WHEN direction = 1 AND inventory.created_at < ? THEN inventory.quantity

                                     ELSE 0

                                 END) AS start_unit,
                  SUM(CASE

                          WHEN direction = 0 AND inventory.created_at < ? THEN inventory.quantity

                          ELSE 0

                      END) - SUM(CASE

                                     WHEN direction = 1 AND inventory.created_at < ? THEN inventory.quantity

                                     ELSE 0

                                 END) AS end_unit
           FROM inventory
           JOIN product ON inventory.product_id = product.id
           WHERE inventory.is_deleted = 0   AND inventory.warehouse_id = ?
           GROUP BY product.id  ORDER BY product.id DESC) AS tb
           WHERE tb.end_unit <> 0 OR tb.start_unit <> 0';

        return DB::select($sql, [$startDate, $startDate, $endDate, $endDate, $warehouseId]);
    }

    public function buildQueryFetchHistory($input): Builder
    {
        $query = Inventory::query()
            ->from(DB::raw('inventory use index (product_id_2)'))
            ->leftJoin('inventory_addition', function ($join) {
                $join->on('inventory.object_id', '=', 'inventory_addition.id')
                    ->whereIn('inventory.object_name', [
                        Inventory::OBJECT_ADDITION,
                        Inventory::OBJECT_REVERT_ADDITION,
                    ]);
            })
            ->leftJoin('employee as employee_addition', 'employee_addition.code', '=', 'inventory_addition.employee_id')
            ->leftJoin('inventory_deduction', function ($join) {
                $join->on('inventory.object_id', '=', 'inventory_deduction.id')
                    ->whereIn('inventory.object_name', [
                        Inventory::OBJECT_DEDUCTION,
                        Inventory::OBJECT_DEDUCTION_SPOILAGE,
                        Inventory::OBJECT_DEDUCTION_UNLINKED,
                        Inventory::OBJECT_REVERT_DEDUCTION,
                    ]);
            })
            ->leftJoin('employee as employee_deduction', 'employee_deduction.id', '=', 'inventory_deduction.employee_id')
            ->leftJoin('test_count', function ($join) {
                $join->on('inventory.object_id', '=', 'test_count.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_TEST_COUNT);
            })
            ->leftJoin('employee as employee_test_count', 'employee_test_count.code', '=', 'test_count.employee_id')
            ->leftJoin('internal_request', function ($join) {
                $join->on('inventory.object_id', '=', 'internal_request.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_INTERNAL_REQUEST);
            })
            ->leftJoin('stock_transfer', function ($join) {
                $join->on('inventory.object_id', '=', 'stock_transfer.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_STOCK_TRANSFER);
            })
            ->leftJoin('employee as employee_stock_transfer', 'employee_stock_transfer.id', '=', 'stock_transfer.employee_id')
            ->leftJoin('adjust_pulling_shelves', function ($join) {
                $join->on('inventory.object_id', '=', 'adjust_pulling_shelves.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_ADJUST_PULLING_SHELVES);
            })
            ->leftJoin('employee as employee_adjust_pulling_shelves', function ($join) {
                $join->on('employee_adjust_pulling_shelves.code', '=', 'adjust_pulling_shelves.employee_id')
                    ->on('employee_adjust_pulling_shelves.warehouse_id', '=', 'adjust_pulling_shelves.warehouse_id');
            })
            ->leftJoin('adjust_shelves_face', function ($join) {
                $join->on('inventory.object_id', '=', 'adjust_shelves_face.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_ADJUST_SHELVE_FACE);
            })
            ->leftJoin('employee as employee_adjust_shelves_face', function ($join) {
                $join->on('employee_adjust_shelves_face.id', '=', 'adjust_shelves_face.employee_id')
                    ->on('employee_adjust_shelves_face.warehouse_id', '=', 'adjust_shelves_face.warehouse_id');
            })
            ->leftJoin('box', 'box.id', '=', 'inventory.box_id')
            ->leftJoin('location', 'location.id', '=', 'inventory.location_id');

        if (!empty($input['product_id'])) {
            $query->where('inventory.product_id', $input['product_id']);
        }

        if (!empty($input['warehouse_id'])) {
            $query->where('inventory.warehouse_id', $input['warehouse_id']);
        }

        if (!empty($input['start_time'])) {
            $query->where('inventory.created_at', '>=', $input['start_time']);
        }

        if (!empty($input['end_time'])) {
            $query->where('inventory.created_at', '<=', $input['end_time']);
        }

        if (!empty($input['direction']) || (isset($input['direction']) && $input['direction'] === (string) Inventory::DIRECTION_INPUT)) {
            $query->where('inventory.direction', $input['direction']);
        }

        if (!empty($input['reason'])) {
            $objectName = $input['reason'];

            switch ($objectName) {
                case 'addition':
                    $query->where('inventory.object_name', Inventory::OBJECT_ADDITION)
                        ->where('inventory.direction', Inventory::DIRECTION_INPUT);

                    break;

                case 'inventory_gain':
                    $query->where('inventory.object_name', Inventory::OBJECT_TEST_COUNT)
                        ->where('inventory.direction', Inventory::DIRECTION_INPUT);

                    break;

                case 'manual_increase':
                    $query->whereIn('inventory.object_name', [
                        Inventory::OBJECT_ADJUST_PULLING_SHELVES,
                        Inventory::OBJECT_ADJUST_SHELVE_FACE,
                    ])
                        ->where('inventory.direction', Inventory::DIRECTION_INPUT);

                    break;

                case 'reversal_out':
                    $query->where('inventory.object_name', Inventory::OBJECT_REVERT_DEDUCTION)
                        ->where('inventory.direction', Inventory::DIRECTION_INPUT);

                    break;

                case 'order_fulfillment':
                    $query->where('inventory.object_name', Inventory::OBJECT_DEDUCTION)
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;

                case 'unlinked_fulfillment':
                    $query->where('inventory.object_name', Inventory::OBJECT_DEDUCTION_UNLINKED)
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;

                case 'spoilage':
                    $query->where('inventory.object_name', Inventory::OBJECT_DEDUCTION_SPOILAGE)
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;

                case 'manual_decrease':
                    $query->whereIn('inventory.object_name', [
                        Inventory::OBJECT_ADJUST_PULLING_SHELVES,
                        Inventory::OBJECT_ADJUST_SHELVE_FACE,
                    ])
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;

                case 'missing_stock':
                    $query->whereIn('inventory.object_name', [
                        Inventory::OBJECT_TEST_COUNT,
                        Inventory::OBJECT_INTERNAL_REQUEST,
                    ])
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;

                case 'transfer_out':
                    $query->where('inventory.object_name', Inventory::OBJECT_STOCK_TRANSFER)
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;

                case 'reversal_in':
                    $query->where('inventory.object_name', Inventory::OBJECT_REVERT_ADDITION)
                        ->where('inventory.direction', Inventory::DIRECTION_OUTPUT);

                    break;
            }
        }

        if (!empty($input['department'])) {
            $query->whereNull('internal_request.id')
                ->where('employee_addition.department', $input['department'])
                ->where('employee_deduction.department', $input['department'])
                ->where('employee_test_count.department', $input['department'])
                ->where('employee_stock_transfer.department', $input['department'])
                ->where('employee_adjust_pulling_shelves.department', $input['department'])
                ->where('employee_adjust_shelves_face.department', $input['department']);
        }

        return $query;
    }

    public function fetchHistory($input): array|int
    {
        if (!empty($input['get_total'])) {
            return $this->buildQueryFetchHistory($input)->count();
        }

        $data = $this->buildQueryFetchHistory($input)
            ->selectRaw("
                inventory.id,
                inventory.direction,
                inventory.product_id,
                inventory.created_at,
                inventory.object_name,
                inventory.quantity,
                inventory.fifo_calculated_at,
                inventory.stock_qty_ending,
                inventory.cost_total,
                inventory.stock_value_ending,
                inventory.object_id,
                inventory_addition.po_number as inventory_addition_po_number,
                inventory_deduction.label_id as inventory_deduction_label_id,
                location.barcode as `location_barcode`,
                box.barcode as `box_barcode`,
                stock_transfer.request_number as `stock_transfer_request_number`,
                CASE
                    WHEN inventory.object_name IN ('" . Inventory::OBJECT_ADDITION . "', '" . Inventory::OBJECT_REVERT_ADDITION . "') THEN employee_addition.name
                    WHEN inventory.object_name IN ('" . Inventory::OBJECT_DEDUCTION . "', '" . Inventory::OBJECT_DEDUCTION_SPOILAGE . "', '" . Inventory::OBJECT_DEDUCTION_UNLINKED . "', '" . Inventory::OBJECT_REVERT_DEDUCTION . "') THEN employee_deduction.name
                    WHEN inventory.object_name = '" . Inventory::OBJECT_TEST_COUNT . "' THEN employee_test_count.name
                    WHEN inventory.object_name = '" . Inventory::OBJECT_STOCK_TRANSFER . "' THEN employee_stock_transfer.name
                    WHEN inventory.object_name = '" . Inventory::OBJECT_ADJUST_PULLING_SHELVES . "' THEN employee_adjust_pulling_shelves.name
                    WHEN inventory.object_name = '" . Inventory::OBJECT_ADJUST_SHELVE_FACE . "' THEN employee_adjust_shelves_face.name
                    ELSE NULL
                END AS employee,
                CASE
                    WHEN inventory.object_name IN ('" . Inventory::OBJECT_ADDITION . "', '" . Inventory::OBJECT_REVERT_ADDITION . "') THEN employee_addition.department
                    WHEN inventory.object_name IN ('" . Inventory::OBJECT_DEDUCTION . "', '" . Inventory::OBJECT_DEDUCTION_SPOILAGE . "', '" . Inventory::OBJECT_DEDUCTION_UNLINKED . "', '" . Inventory::OBJECT_REVERT_DEDUCTION . "') THEN employee_deduction.department
                    WHEN inventory.object_name = '" . Inventory::OBJECT_TEST_COUNT . "' THEN employee_test_count.department
                    WHEN inventory.object_name = '" . Inventory::OBJECT_STOCK_TRANSFER . "' THEN employee_stock_transfer.department
                    WHEN inventory.object_name = '" . Inventory::OBJECT_ADJUST_PULLING_SHELVES . "' THEN employee_adjust_pulling_shelves.department
                    WHEN inventory.object_name = '" . Inventory::OBJECT_ADJUST_SHELVE_FACE . "' THEN employee_adjust_shelves_face.department
                    ELSE NULL
                END AS department
            ")
            ->orderByDesc('inventory.created_at')
            ->orderByDesc('inventory.id')
            ->simplePaginate($input['limit'] ?? 25);

        foreach ($data as $item) {
            $this->bindingHistoryItem($item);
        }

        $firstItemData = $this->buildQueryFetchHistory([
            'end_time' => $input['end_time'],
            'product_id' => $input['product_id'],
            'warehouse_id' => $input['warehouse_id'],
        ])
            ->selectRaw('
                    inventory.stock_qty_ending,
                    inventory.stock_value_ending
                ')
            ->where('inventory.fifo_calculated_at', '<>', Inventory::FIFO_NOT_CALCULATED)
            ->orderByDesc('inventory.created_at')
            ->orderByDesc('inventory.id')
            ->first();

        return [
            'data' => $data->items(),
            'stock_qty_ending' => $firstItemData->stock_qty_ending ?? 0,
            'stock_value_ending' => $firstItemData->stock_value_ending ?? 0,
        ];
    }

    public function bindingHistoryItem(&$item, $isPaginator = true): void
    {
        $key = $item->object_name . $item->direction;
        $note = [];

        switch ($key) {
            case Inventory::OBJECT_ADDITION . Inventory::DIRECTION_INPUT:
                $reason = 'Addition';
                $note[] = "PO #$item->inventory_addition_po_number";
                $item->box_barcode && $note[] = "Box: $item->box_barcode";

                break;

            case Inventory::OBJECT_TEST_COUNT . Inventory::DIRECTION_INPUT:
                $reason = 'Inventory Gain';
                $note[] = "Test Count ID: $item->object_id";
                $item->location_barcode && $note[] = "Location: $item->location_barcode";
                $item->box_barcode && $note[] = "Box: $item->box_barcode";

                break;

            case Inventory::OBJECT_ADJUST_PULLING_SHELVES . Inventory::DIRECTION_INPUT:
                $reason = 'Manual Increase';
                $note[] = "Pulling Adjust ID: $item->object_id";

                break;

            case Inventory::OBJECT_ADJUST_SHELVE_FACE . Inventory::DIRECTION_INPUT:
                $reason = 'Manual Increase';
                $note[] = "DarkPods Adjust ID: $item->object_id";

                break;

            case Inventory::OBJECT_REVERT_DEDUCTION . Inventory::DIRECTION_INPUT:
                $reason = 'Reversal Out';
                $note[] = "Deduction ID: $item->object_id";
                $item->inventory_deduction_label_id && $note[] = "Label: $item->inventory_deduction_label_id";

                break;

            case Inventory::OBJECT_DEDUCTION . Inventory::DIRECTION_OUTPUT:
                $reason = 'Order Fulfillment';
                $note[] = "Deduction ID: $item->object_id";
                $item->inventory_deduction_label_id && $note[] = "Label: $item->inventory_deduction_label_id";

                break;

            case Inventory::OBJECT_DEDUCTION_UNLINKED . Inventory::DIRECTION_OUTPUT:
                $reason = 'Unlinked Fulfillment';
                $note[] = "Deduction ID: $item->object_id";
                $item->inventory_deduction_label_id && $note[] = "Label: $item->inventory_deduction_label_id";

                break;

            case Inventory::OBJECT_DEDUCTION_SPOILAGE . Inventory::DIRECTION_OUTPUT:
                $reason = 'Spoilage';
                $note[] = "Deduction ID: $item->object_id";
                $item->inventory_deduction_label_id && $note[] = "Label: $item->inventory_deduction_label_id";

                break;

            case Inventory::OBJECT_ADJUST_PULLING_SHELVES . Inventory::DIRECTION_OUTPUT:
                $reason = 'Manual Decrease';
                $note[] = "Pulling Adjust ID: $item->object_id";

                break;

            case Inventory::OBJECT_ADJUST_SHELVE_FACE . Inventory::DIRECTION_OUTPUT:
                $reason = 'Manual Decrease';
                $note[] = "DarkPods Adjust ID: $item->object_id";

                break;

            case Inventory::OBJECT_TEST_COUNT . Inventory::DIRECTION_OUTPUT:
                $reason = 'Missing Stock';
                $note[] = "Test Count ID: $item->object_id";
                $item->location_barcode && $note[] = "Location: $item->location_barcode";
                $item->box_barcode && $note[] = "Box: $item->box_barcode";

                break;

            case Inventory::OBJECT_INTERNAL_REQUEST . Inventory::DIRECTION_OUTPUT:
                $reason = 'Missing Stock';
                $note[] = "Internal Request ID: $item->object_id";
                $item->location_barcode && $note[] = "Location: $item->location_barcode";
                $item->box_barcode && $note[] = "Box: $item->box_barcode";

                break;

            case Inventory::OBJECT_STOCK_TRANSFER . Inventory::DIRECTION_OUTPUT:
                $reason = 'Transfer Out';
                $note[] = "Request #$item->stock_transfer_request_number";

                break;

            case Inventory::OBJECT_REVERT_ADDITION . Inventory::DIRECTION_OUTPUT:
                $reason = 'Reversal In';
                $note[] = "PO #$item->inventory_addition_po_number";
                $item->box_barcode && $note[] = "Box: $item->box_barcode";

                break;

            default:
                $reason = '';

                break;
        }

        $item->reason = $reason;
        $item->note = $isPaginator ? implode(' <br/> ', $note) : implode(' - ', $note);
    }
}
