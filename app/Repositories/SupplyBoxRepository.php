<?php

namespace App\Repositories;

use App\Models\SupplyBox;

class SupplyBoxRepository extends CommonRepository
{
    public function fetchBox($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = SupplyBox::with([
            'supply:id,name,sku',
            'location:id,barcode'
        ])
        ->where('supply_boxes.warehouse_id', config('jwt.warehouse_id'));

        if (!empty($input['is_fetch_all'])) {
            return $query->get();
        }

        if (!empty($input['supply_name']) || !empty($input['supply_sku'])) {
            $query->whereHas('supply', function ($q) use ($input) {
                if (!empty($input['supply_name'])) {
                    $q->where('name', 'LIKE', '%' . $input['supply_name'] . '%');
                }
                if (!empty($input['supply_sku'])) {
                    $q->where('sku', 'LIKE', '%' . $input['supply_sku'] . '%');
                }
            });
        }

        if (!empty($input['box_id'])) {
            $query->where('barcode', 'LIKE', '%' . $input['box_id'] . '%');
        }

        if (!empty($input['location_id'])) {
            $query->whereHas('location', function ($q) use ($input) {
                $q->where('barcode', '=', $input['location_id']);
            });
        }

        return $query->orderBy('id', 'desc')->paginate($limit);
    }

    public function getBoxes($input)
    {
        $query = SupplyBox::with([
            'supply:id,name,sku'
        ])
        ->where('warehouse_id', config('jwt.warehouse_id'));

        if (!empty($input['supply_sku'])) {
            $query->whereHas('supply', function ($q) use ($input) {
                $q->where('sku', 'LIKE', '%' . $input['supply_sku'] . '%');
            });
        }

        return $query->get();
    }

    public function getBoxInfo($input)
    {
        return SupplyBox::with(['location:id,barcode', 'supply:id,name'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('barcode', $input['barcode'])->first();
    }

    public function getBoxInLocationForTestCount($input)
    {
        return SupplyBox::with(['location:id,barcode', 'supply:id,name'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('location_id', $input['location_id'])->get();
    }
}
