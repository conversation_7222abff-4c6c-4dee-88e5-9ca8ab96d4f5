<?php

namespace App\Repositories;

class GiftMessageRepository
{
    function createGiftCard($text) {
        // Create a blank image with the specified dimensions
        $width = 6 * 300;  // 6 inches converted to pixels at 300 DPI
        $height = 4 * 300; // 4 inches converted to pixels at 300 DPI
        $image = imagecreatetruecolor($width, $height);

        // Đường dẫn tới hình ảnh trong thư mục public
        $imagePath = public_path('gift_card.jpg');

        // Load the background image
        $backgroundImage = imagecreatefromjpeg($imagePath);

        // Resize the background image to fit the gift card dimensions
        imagecopyresized($image, $backgroundImage, 0, 0, 0, 0, $width, $height, imagesx($backgroundImage), imagesy($backgroundImage));

        // Define colors for the text
        $textColor = imagecolorallocate($image, 0, 0, 0); // Black

        // Calculate the dimensions and position of the text
        $fontSize = 36; // Adjust as needed
        $fontPath = resource_path('fonts/Metal-Regular.ttf');
        $maxTextWidth = $width * 1.5; // Maximum width of the text
        $lineHeight = 1.5 * $fontSize; // Vertical spacing between lines
        $textLines = explode("\n", wordwrap($text, $maxTextWidth / $fontSize, "\n", false)); // Split text into multiple lines
        $textHeight = count($textLines) * $lineHeight;
        $textX = ($width - $maxTextWidth) / 2; // Center horizontally
        $textY = ($height - $textHeight) / 2; // Center vertically
        // Add each line of text
        foreach ($textLines as $line) {
            $lineWidth = imagettfbbox($fontSize, 0, $fontPath, $line)[2] - imagettfbbox($fontSize, 0, $fontPath, $line)[0];
            $lineX = $textX + ($maxTextWidth - $lineWidth) / 2; // Center the line horizontally within the max width
            imagettftext($image, $fontSize, 0, $lineX, $textY, $textColor, $fontPath, $line);
            $textY += $lineHeight; // Move to the next line
        }

        $image = imagerotate($image, 90, 0);

        // Output the image
        header('Content-Type: image/png');
        imagepng($image);

        // Clean up memory
        imagedestroy($image);
        imagedestroy($backgroundImage);
    }

}
