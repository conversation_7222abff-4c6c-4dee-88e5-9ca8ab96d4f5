<?php

namespace App\Services;

use App\Models\QuickBookAuthenticate;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use QuickBooksOnline\API\Data\IPPCompanyInfo;
use QuickBooksOnline\API\Exception\SdkException;

class QBAuthService extends QBBaseService
{
    const ACCESS_TOKEN_KEY = 'QUICK_BOOK_ACCESS_TOKEN_KEY';

    const ENTITY_TYPE = 'Auth';

    public function __construct()
    {
        parent::__construct();
    }

    public function login()
    {
        $dataService = $this->configureAuth();
        $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
        $url = $OAuth2LoginHelper->getAuthorizationCodeURL();

        if (empty($url)) {
            $this->logLastError($dataService, 0, self::ENTITY_TYPE, __METHOD__);
        }

        return $url;
    }

    public function callback($request)
    {
        $dataService = $this->configureAuth();

        try {
            $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
            $accessToken = $OAuth2LoginHelper->exchangeAuthorizationCodeForToken($request['code'], $request['realmId']);
            $authInfo = QuickBookAuthenticate::find($this->getAuthInfo()->id);
            $authInfo->qb_refresh_token = $accessToken->getRefreshToken();
            $authInfo->qb_refresh_token_expires_at = $accessToken->getRefreshTokenExpiresAt();
            $authInfo->qb_realm_id = $accessToken->getRealmID();
            $authInfo->save();
            Cache::put(self::ACCESS_TOKEN_KEY, $accessToken->getAccessToken(), 3600);
            Log::info('QBAuthService.loginCallBack callback success', $request);

            return true;
        } catch (\Exception $e) {
            Log::error('QBAuthService.loginCallBack', [
                'exception' => $e,
                'QB_last_error' => $dataService->getLastError()
            ]);

            $this->logLastError($dataService, 0, self::ENTITY_TYPE, __METHOD__, $request);

            return false;
        }
    }

    /**
     * @throws SdkException
     */
    public function getCompanyInfo(): ?IPPCompanyInfo
    {
        return $this->configureData()->getCompanyInfo();
    }
}
