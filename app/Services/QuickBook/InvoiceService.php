<?php

namespace App\Services\QuickBook;

use App\Models\QuickBookCompany;
use App\Models\QuickBookItem;
use Illuminate\Support\Facades\Storage;
use QuickBooksOnline\API\Data\IPPAttachable;
use QuickBooksOnline\API\Data\IPPAttachableRef;
use QuickBooksOnline\API\Data\IPPItem;
use QuickBooksOnline\API\Data\IPPInvoice;
use QuickBooksOnline\API\Data\IPPReferenceType;
use QuickBooksOnline\API\DataService\DataService;
use QuickBooksOnline\API\Facades\Invoice as QuickBookInvoice;
use QuickBooksOnline\API\Facades\Item;

class InvoiceService
{
    public function __construct(
        protected QuickBookCompany $company,
        protected DataService $quickBookService
    ) {
        //
    }

    public function findInvoiceById($id)
    {
        return $this->quickBookService->FindById('Invoice', $id);
    }

    public function listInvoices(array $filters = [])
    {
        $query = "SELECT * FROM Invoice";
        if (!empty($filters)) {
            $query .= " WHERE " . implode(' AND ', $filters);
        }

        $query .= " ORDER BY MetaData.CreateTime DESC";

        return $this->quickBookService->Query($query);
    }

    public function createInvoice(array $payload)
    {
        $invoice = QuickBookInvoice::create($payload);
        $response = $this->quickBookService->Add($invoice);

        return $response;
    }

    public function updateInvoice($invoiceId, array $payload)
    {
        /** @var IPPInvoice $invoice */
        $invoice = $this->quickBookService->FindById('Invoice', $invoiceId);

        $response = $this->quickBookService->Update(QuickBookInvoice::update($invoice, $payload));

        return $response;
    }

    public function sendInvoice(IPPInvoice $invoice, string $email)
    {
        return $this->quickBookService->SendEmail($invoice, $email);
    }

    public function uploadFile(array $files, $invoiceId)
    {
        foreach ($files as $fileItem) {
            $fileData = Storage::disk('local')->get($fileItem['filePath']);
            $filename = basename($fileItem['filePath']);
            $mimeType = $fileItem['mimeType'];

            $attachment = new IPPAttachable();
            $attachment->FileName = $filename;
            $attachment->ContentType = $mimeType;

            $entityRef = new IPPReferenceType();
            $entityRef->type = "Invoice";
            $entityRef->value = $invoiceId;

            $attachableRef = new IPPAttachableRef();
            $attachableRef->EntityRef = $entityRef;
            $attachableRef->IncludeOnSend = true;

            $attachment->AttachableRef = [$attachableRef];

            $this->quickBookService->Upload(
                base64_encode($fileData),
                $filename,
                $mimeType,
                $attachment
            );
        }
    }

    public function syncFile(array $files, $invoiceId)
    {
        $fileExists = $this->quickBookService->Query("
            SELECT * FROM Attachable 
            WHERE AttachableRef.EntityRef.Type = 'Invoice' 
            AND AttachableRef.EntityRef.Value = '{$invoiceId}'");

        foreach ($fileExists ?? [] as $file) {
            $file->AttachableRef->EntityRef = null;
            $this->quickBookService->Delete($file);
        }

        $this->uploadFile($files, $invoiceId);
    }

    public function getInvoiceItems()
    {
        $query = "SELECT * FROM Item WHERE Type IN ('Category')";

        $query .= " ORDER BY MetaData.CreateTime DESC";

        return $this->quickBookService->Query($query);
    }

    public function findItemByInvoiceId($id)
    {
        return $this->quickBookService->FindById('Item', $id);
    }

    public function createItem($name, IPPItem $category) : IPPItem
    {
        $itemEntityData = [
            "Name" => $name,
            "Type" => "Service",
            "IncomeAccountRef" => [
                "value" => "79",
                "name" => "Sales of Product Income"
            ],
        ];

        if (isset($category)) {
            $itemEntityData["SubItem"] = true;
            $itemEntityData["ParentRef"] = [
                "value" => $category->Id
            ];
        }

        $existingItem = $this->quickBookService->Query("SELECT * FROM Item WHERE Name = '$name' AND Type = 'Service' AND ParentRef.value = '{$category->Id}'");

        if (!empty($existingItem)) {
            return collect($existingItem)->first();
        }

        $item = $this->quickBookService->Add(Item::create($itemEntityData));

        return $item;
    }

    public function getItemInvoiceCategory()
    {
        $query = "SELECT * FROM Item WHERE Type IN ('Category') AND Name = 'Category Invoice Items'";

        $items = $this->quickBookService->Query($query);

        if (empty($items)) {
            return $this->createItemCategory();
        }

        return collect($items)->first();
    }

    public function createItemCategory()
    {
        $itemCategoryEntity = Item::create([
            "Name" => "Category Invoice Items",
            "Type" => "Category"
        ]);

        $item = $this->quickBookService->Add($itemCategoryEntity);

        return $item;
    }

    public function getInvoiceLineItems($data)
    {
        $internalCategory = QuickBookItem::where('spa_type', QuickBookItem::SPA_TYPE_INVOICE)
            ->where('qb_parent_id', QuickBookItem::IS_PARENT_ID)
            ->first();

        if (empty($internalCategory)) {
            $itemCategory = $this->getItemInvoiceCategory();
            $internalCategory = QuickBookItem::create([
                'qb_item_id' => $itemCategory->Id,
                'qb_name' => $itemCategory->Name,
                'qb_type' => $itemCategory->Type,
                'spa_type' => QuickBookItem::SPA_TYPE_INVOICE,
            ]);
        }

        $internalItems = QuickBookItem::where('spa_type', QuickBookItem::SPA_TYPE_INVOICE)
            ->where('qb_parent_id', $internalCategory->qb_item_id)
            ->get();

        $invoiceLineItems = [];
        foreach ($data as $itemData) {
            if ($itemData['description'] === 'promotions') {
                $invoiceLineItems[] = [
                    "DetailType" => "DiscountLineDetail",
                    "Amount" => $itemData['amount'],
                    "DiscountLineDetail" => [
                        "PercentBased" => false
                    ]
                ];

                continue;
            }

            $internalItem = $internalItems->firstWhere('qb_name', $itemData['description']);
            if (empty($internalItem)) {
                $itemCategory = $this->findItemByInvoiceId($internalCategory->qb_item_id);
                $quickBookItem = $this->createItem($itemData['description'], $itemCategory);
                $internalItem = QuickBookItem::create([
                    'qb_item_id' => $quickBookItem->Id,
                    'qb_name' => $quickBookItem->Name,
                    'qb_type' => $quickBookItem->Type,
                    'spa_type' => QuickBookItem::SPA_TYPE_INVOICE,
                    'qb_parent_id' => $internalCategory->qb_item_id,
                ]);
            }

            $invoiceLineItems[] = [
                "Amount" => $itemData['amount'],
                "DetailType" => "SalesItemLineDetail",
                "SalesItemLineDetail" => [
                    "Qty" => $itemData['units'],
                    "UnitPrice" => $itemData['amount'] / $itemData['units'],
                    "TaxCodeRef" => [
                        "value" => "NON"
                    ],
                    "ItemRef" => [
                        "value" => $internalItem->qb_item_id,
                        "name" => $internalItem->qb_name
                    ]
                ]
            ];
        }

        return $invoiceLineItems;
    }
}
