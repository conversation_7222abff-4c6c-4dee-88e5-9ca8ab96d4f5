<?php

namespace App\Services\QuickBook;

use App\Models\QuickBookCompany;
use Illuminate\Support\Facades\Log;
use QuickBooksOnline\API\Data\IPPCustomer;
use QuickBooksOnline\API\DataService\DataService;
use QuickBooksOnline\API\Facades\Customer as QuickBookCustomer;

class CustomerService
{
    public function __construct(
        protected QuickBookCompany $company,
        protected DataService $quickBookService
    ) {
        //
    }

    public function createCustomer(array $payload): IPPCustomer|null
    {
        $customerData = QuickBookCustomer::create($payload);
        $customer = $this->quickBookService->Add($customerData);

        return $customer;
    }

    public function updateCustomer(string $customerId, array $payload): IPPCustomer|null
    {
        /** @var IPPCustomer $customer */
        $customer = $this->quickBookService->FindById('Customer', $customerId);

        $this->quickBookService->Update(QuickBookCustomer::update($customer, $payload));

        return $customer;
    }

    public function findCustomerById(string $customerId): ?IPPCustomer
    {
        return $this->quickBookService->FindById('Customer', $customerId);
    }
}
