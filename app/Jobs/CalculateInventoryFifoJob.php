<?php

namespace App\Jobs;

use App\Models\Inventory;
use App\Models\InventoryReference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateInventoryFifoJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const QUEUE_NAME = 'inventory-fifo-calculate-job';

    public string $productId;

    public string $warehouseId;

    public int $limit;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($productId, $warehouseId, $limit = 5000)
    {
        $this->productId = $productId;
        $this->warehouseId = $warehouseId;
        $this->limit = $limit;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("CalculateInventoryFifoJob.handle: Processing FIFO for product {$this->productId} and warehouse {$this->warehouseId}");

        try {
            Inventory::query()
                ->from(DB::raw('inventory USE INDEX (product_id)'))
                ->where('product_id', $this->productId)
                ->where('warehouse_id', $this->warehouseId)
                ->where('fifo_calculated_at', Inventory::FIFO_NOT_CALCULATED)
                ->orderBy('created_at')
                ->orderBy('id')
                ->chunkById($this->limit, function ($inventories) {
                    $lastInventory = Inventory::query()
                        ->from(DB::raw('inventory USE INDEX (product_id)'))
                        ->where('fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
                        ->where('product_id', $this->productId)
                        ->where('warehouse_id', $this->warehouseId)
                        ->orderByDesc('created_at')
                        ->orderByDesc('id')
                        ->first();
                    $lastStockQtyEnding = $lastInventory->stock_qty_ending ?? 0;
                    $lastStockValueEnding = $lastInventory->stock_value_ending ?? 0;
                    $dataStockFifo = $this->getStockFifo();

                    foreach ($inventories as $item) {
                        DB::beginTransaction();

                        try {
                            $key = $item->object_name . $item->direction;

                            switch ($key) {
                                case Inventory::OBJECT_ADDITION . Inventory::DIRECTION_INPUT:
                                case Inventory::OBJECT_TEST_COUNT . Inventory::DIRECTION_INPUT:
                                case Inventory::OBJECT_ADJUST_PULLING_SHELVES . Inventory::DIRECTION_INPUT:
                                case Inventory::OBJECT_ADJUST_SHELVE_FACE . Inventory::DIRECTION_INPUT:
                                    Inventory::query()
                                        ->where('id', $item->id)
                                        ->lockForUpdate()
                                        ->first();
                                    $item->fifo_calculated_at = now('UTC')->format('YmdHis');
                                    $item->stock_qty_ending = $lastStockQtyEnding + $item->quantity;
                                    $item->remaining_qty = max(min($item->stock_qty_ending, $item->quantity), 0);
                                    $costRemaining = (!empty($item->cost_total) ? $item->cost_total : 0) / $item->quantity * $item->remaining_qty;
                                    $item->stock_value_ending = $lastStockValueEnding + $costRemaining;

                                    if (!empty($item->is_deleted)) {
                                        $item->remaining_qty = 0;
                                    }

                                    $item->save();

                                    break;

                                case Inventory::OBJECT_REVERT_DEDUCTION . Inventory::DIRECTION_INPUT:
                                    $firstRevert = true;
                                    $reference = InventoryReference::query()
                                        ->where('inventory_id', $item->replicated_from_id)
                                        ->orderBy('id')
                                        ->get();

                                    if (empty($reference)) {
                                        throw new \Exception("No input stock found for FIFO deduction, Key: $key. Inventory ID: " . $item->id);
                                    }

                                    $inventoryReferences = [];
                                    $fifoCalculatedAt = now('UTC')->format('YmdHis');

                                    foreach ($reference as $revertItem) {
                                        $lastStockQtyEnding += $revertItem->quantity;
                                        $lastStockValueEnding += (!empty($revertItem->cost_total) ? $revertItem->cost_total : 0);
                                        $costTotal = !empty($revertItem->cost_total) ? $revertItem->cost_total : 0;
                                        $qty = $revertItem->quantity;

                                        if ($firstRevert) {
                                            $firstRevert = false;
                                            $item->quantity = $qty;
                                            $item->remaining_qty = $lastStockQtyEnding > 0 ? $qty : 0;
                                            $item->fifo_calculated_at = $fifoCalculatedAt;
                                            $item->stock_qty_ending = $lastStockQtyEnding;
                                            $item->stock_value_ending = $lastStockValueEnding;
                                            $item->cost_total = $costTotal;
                                            $item->save();

                                            $inventoryReferences[] = [
                                                'inventory_id' => $item->id,
                                                'referenced_inventory_id' => $revertItem->inventory_id,
                                                'quantity' => $qty,
                                                'cost_total' => $item->cost_total,
                                                'created_at' => now(),
                                                'updated_at' => now(),
                                            ];

                                            continue;
                                        }

                                        $newInventory = $item->replicate();
                                        $newInventory->quantity = $qty;
                                        $newInventory->remaining_qty = $qty;
                                        $newInventory->fifo_calculated_at = $fifoCalculatedAt;
                                        $newInventory->stock_qty_ending = $lastStockQtyEnding;
                                        $newInventory->stock_value_ending = $lastStockValueEnding;
                                        $newInventory->cost_total = $costTotal;
                                        $newInventory->replicated_from_id = $item->id;
                                        $newInventory->is_deleted = 1;
                                        $newInventory->save();

                                        $inventoryReferences[] = [
                                            'inventory_id' => $newInventory->id,
                                            'referenced_inventory_id' => $revertItem->inventory_id,
                                            'quantity' => $qty,
                                            'cost_total' => $costTotal,
                                            'created_at' => now(),
                                            'updated_at' => now(),
                                        ];
                                    }

                                    if (!empty($inventoryReferences)) {
                                        InventoryReference::query()->insert($inventoryReferences);
                                    }

                                    break;

                                case Inventory::OBJECT_DEDUCTION . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_DEDUCTION_UNLINKED . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_DEDUCTION_SPOILAGE . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_ADJUST_PULLING_SHELVES . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_ADJUST_SHELVE_FACE . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_TEST_COUNT . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_STOCK_TRANSFER . Inventory::DIRECTION_OUTPUT:
                                case Inventory::OBJECT_INTERNAL_REQUEST . Inventory::DIRECTION_OUTPUT:
                                    Inventory::query()
                                        ->where('id', $item->id)
                                        ->lockForUpdate()
                                        ->first();
                                    $qtyNeedDeduct = $item->quantity;
                                    $costTotal = 0;
                                    $inventoryReferences = [];

                                    while ($qtyNeedDeduct > 0) {
                                        if (empty($dataStockFifo) && $lastStockQtyEnding >= 0) {
                                            $dataStockFifo = $this->getStockFifo();
                                        }

                                        $firstFifo = $dataStockFifo[0] ?? null;

                                        if (!empty($firstFifo)) {
                                            $lockInventoryFifo = Inventory::query()
                                                ->selectRaw('
                                                    id,
                                                    remaining_qty,
                                                    COALESCE(cost_total / quantity, 0) as unit_cost_value
                                                ')
                                                ->where('id', $firstFifo['id'])
                                                ->lockForUpdate()
                                                ->first();

                                            if ($lockInventoryFifo->remaining_qty <= 0) {
                                                array_shift($dataStockFifo);

                                                continue;
                                            }

                                            $qtyDeduct = min($qtyNeedDeduct, $lockInventoryFifo->remaining_qty);
                                            $qtyNeedDeduct -= $qtyDeduct;
                                            $costTotal += $lockInventoryFifo->unit_cost_value * $qtyDeduct;
                                            $lockInventoryFifo->remaining_qty -= $qtyDeduct;
                                            $lockInventoryFifo->save();

                                            if ($lockInventoryFifo->remaining_qty <= 0) {
                                                array_shift($dataStockFifo);
                                            }

                                            $inventoryReferences[] = [
                                                'inventory_id' => $item->id,
                                                'referenced_inventory_id' => $lockInventoryFifo->id ?? 0,
                                                'quantity' => $qtyDeduct,
                                                'cost_total' => $lockInventoryFifo->unit_cost_value * $qtyDeduct,
                                                'created_at' => now(),
                                                'updated_at' => now(),
                                            ];
                                        } else {
                                            $inventoryReferences[] = [
                                                'inventory_id' => $item->id,
                                                'referenced_inventory_id' => 0,
                                                'quantity' => $qtyNeedDeduct,
                                                'cost_total' => 0,
                                                'created_at' => now(),
                                                'updated_at' => now(),
                                            ];
                                            $qtyNeedDeduct = 0;
                                        }
                                    }

                                    $costTotal = round($costTotal, 2);
                                    $item->fifo_calculated_at = now('UTC')->format('YmdHis');
                                    $item->stock_qty_ending = $lastStockQtyEnding - $item->quantity;
                                    $item->stock_value_ending = $lastStockValueEnding - $costTotal;
                                    $item->cost_total = $costTotal;
                                    $item->save();

                                    if (!empty($inventoryReferences)) {
                                        InventoryReference::query()->insert($inventoryReferences);
                                    }

                                    break;

                                case Inventory::OBJECT_REVERT_ADDITION . Inventory::DIRECTION_OUTPUT:
                                    $item->fifo_calculated_at = now('UTC')->format('YmdHis');
                                    $item->remaining_qty = 0;
                                    $item->stock_qty_ending = $lastStockQtyEnding - $item->quantity;
                                    $item->stock_value_ending = max($lastStockValueEnding - (!empty($item->cost_total) ? $item->cost_total : 0), 0);
                                    $item->save();

                                    Inventory::query()
                                        ->where('id', $item->replicated_from_id)
                                        ->update(['remaining_qty' => 0]);

                                    break;

                                default:
                                    throw new \Exception("Key data fifo invalid, Key: $key. Inventory ID: " . $item->id);
                            }

                            DB::commit();
                        } catch (\Throwable $e) {
                            DB::rollBack();

                            throw $e;
                        }

                        $lastStockQtyEnding = $item->stock_qty_ending ?? 0;
                        $lastStockValueEnding = $item->stock_value_ending ?? 0;
                    }
                });
        } catch (\Throwable $e) {
            Log::error('CalculateInventoryFifoJob.handle: FIFO calculation failed', [
                'product_id' => $this->productId,
                'warehouse_id' => $this->warehouseId,
                'exception' => $e,
            ]);
        } finally {
            $cacheKey = "CalculateInventoryFifoJob:{$this->productId}:{$this->warehouseId}";
            Cache::store(config('cache.redis_store'))->forget($cacheKey);
        }
    }

    public function getStockFifo(): array
    {
        return Inventory::query()
            ->from(DB::raw('inventory USE INDEX (product_id)'))
            ->select('id')
            ->where('fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->where('warehouse_id', $this->warehouseId)
            ->where('product_id', $this->productId)
            ->where('direction', Inventory::DIRECTION_INPUT)
            ->where('remaining_qty', '>', 0)
            ->orderBy('created_at')
            ->orderBy('id')
            ->limit(500)
            ->get()
            ->toArray();
    }

    public function failed(\Throwable $exception): void
    {
        $cacheKey = "CalculateInventoryFifoJob:{$this->productId}:{$this->warehouseId}";
        Cache::store(config('cache.redis_store'))->forget($cacheKey);
    }
}
