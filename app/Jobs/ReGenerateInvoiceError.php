<?php

namespace App\Jobs;

use App\Console\Commands\CalculatePriceForSeller;
use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\InvoiceSaleOrderError;
use App\Models\InvoiceSaleOrderInsert;
use App\Models\PeakShippingFee;
use App\Models\Product;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderCalculateFailed;
use App\Models\SaleOrderInsert;
use App\Models\SaleOrderInsertCalculatePrice;
use App\Models\SaleOrderInsertSku;
use App\Models\SaleOrderItemCalculateFailed;
use App\Models\Shipment;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\Tag;
use App\Repositories\InvoiceRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Console\OutputStyle;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\NullOutput;

class ReGenerateInvoiceError implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 1800; // Set the timeout to 1800 seconds (30 minutes)

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected $invoiceId)
    {
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        echo "Start regenerate invoice id: $this->invoiceId" . PHP_EOL;

        $invoice = Invoice::find($this->invoiceId);

        if (!$invoice) {
            echo 'Invoice not found' . PHP_EOL;

            return;
        }

        $store = Store::find($invoice->store_id);
        $productPrintSides = ProductPrintSide::all();

        if ($invoice->re_calculate_price_at) {
            $productPrintAreas = ProductPrintArea::all();
            $this->reCalculatePriceForOrder($invoice, $productPrintSides); //calculate price order in invoice_sale_order table
            $this->calculatePriceForOrder($invoice, $productPrintSides); //calculate price order in invoice_sale_order_error table
            $this->calculatePriceForInsertOrder($invoice, $store, $productPrintSides, $productPrintAreas, true);
            $this->updateStoreAndExportInvoice($invoice);
            $this->updateStoreAndExportInsertInvoice($invoice);
        } else {
            if ($invoice->has_error == Invoice::HAS_ERROR) {
                $this->calculatePriceForOrder($invoice, $productPrintSides);
                $this->updateStoreAndExportInvoice($invoice);
            }
            if ($invoice->has_error_insert_invoice == Invoice::HAS_ERROR) {
                $productPrintAreas = ProductPrintArea::all();
                $this->calculatePriceForInsertOrder($invoice, $store, $productPrintSides, $productPrintAreas);

                $invoiceRepository = resolve(InvoiceRepository::class);
                echo "Dispatch generate new pdf invoice#$invoice->id" . PHP_EOL;
                $invoiceRepository->dispatchJobGenerateInvoice($invoice);
                echo "Dispatch generate new surcharge invoice#$invoice->id" . PHP_EOL;
                $invoiceRepository->dispatchJobGenerateSurchargeInvoice($invoice);
                echo "Dispatch generate new insert invoice#$invoice->id" . PHP_EOL;
                $invoiceRepository->dispatchJobGenerateInsertInvoice($invoice);
            }
        }
    }

    public function calculatePriceForOrder($invoice, $productPrintSides)
    {
        $i = 1;
        $invoiceSaleOrderErrors = [];
        $peakShippingFee = PeakShippingFee::first();
        $tagAdditionalService = Tag::select('id', 'name')->where('is_additional_service', true)->get();
        $tagLabel = Tag::where('name', Tag::TAG_LABEL)->first();
        InvoiceSaleOrderError::select('sale_order_id')
            ->where('invoice_id', $invoice->id)
            ->orderBy('sale_order_id')
            ->chunk(100, function ($chunks) use (&$i, &$invoiceSaleOrderErrors) {
                echo("Chunk $i has " . $chunks->count() . ' sale orders start...') . PHP_EOL;
                foreach ($chunks as $chunk) {
                    $invoiceSaleOrderErrors[] = $chunk->sale_order_id;
                }
                echo("Chunk $i ended.") . PHP_EOL;
                $i++;
            });

        foreach ($invoiceSaleOrderErrors as $saleOrderId) {
            try {
                $saleOrder = SaleOrder::with([
                    'items',
                    'shipmentDefault',
                    'addressSaleOrder' => function ($queryAddress) {
                        $queryAddress->where('type_address', SaleOrderAddress::TO_ADDRESS);
                    }
                ])->where('id', $saleOrderId)->first();

                if (!$saleOrder) {
                    echo("Sale order id: $saleOrderId not found") . PHP_EOL;

                    continue;
                }
                echo("Update sale order id: $saleOrder->id start...") . PHP_EOL;
                echo('Sale order has: ' . $saleOrder->items->count() . ' items') . PHP_EOL;
                if ($saleOrder->calculated_at) {
                    echo("Sale order id: $saleOrderId has been calculated") . PHP_EOL;
                    SaleOrderItemCalculateFailed::where('order_id', $saleOrder->id)->delete();
                    SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->delete();
                    InvoiceSaleOrderError::where('sale_order_id', $saleOrder->id)->where('invoice_id', $invoice->id)->delete();
                    $invoiceSaleOrder = InvoiceSaleOrder::where('sale_order_id', $saleOrder->id)->where('invoice_id', $invoice->id)->first();
                    if (!$invoiceSaleOrder) {
                        InvoiceSaleOrder::create(['sale_order_id' => $saleOrder->id, 'invoice_id' => $invoice->id]);
                    }

                    continue;
                }
                $calculatePriceService = new CalculatePriceForSeller();
                $calculatePriceService->setOutput(new OutputStyle(new ArgvInput(), new NullOutput()));
                $priceCalculationStatus = $calculatePriceService->handleCalculatePriceOrderViaPricingSnapshot($saleOrder, $productPrintSides, $tagAdditionalService, $peakShippingFee, $tagLabel, true);
                if ($priceCalculationStatus) {
                    InvoiceSaleOrderError::where('sale_order_id', $saleOrder->id)->where('invoice_id', $invoice->id)->delete();
                    $invoiceSaleOrder = InvoiceSaleOrder::where('sale_order_id', $saleOrder->id)
                        ->where('invoice_id', $invoice->id)->first();
                    if (!$invoiceSaleOrder) {
                        InvoiceSaleOrder::create(['sale_order_id' => $saleOrder->id, 'invoice_id' => $invoice->id]);
                    }
                }
                echo("Update sale order id: $saleOrder->id done.") . PHP_EOL;
            } catch (\Throwable $th) {
                echo $th->getMessage() . PHP_EOL;
                SaleOrderCalculateFailed::updateOrCreate([
                    'order_id' => $saleOrderId,
                    'store_id' => $invoice->store_id,
                ], [
                    'failed_at' => now(),
                ]);
            }
        }

        $invoiceHasError = InvoiceSaleOrderError::where('invoice_id', $invoice->id)->exists();
        $hasError = $invoiceHasError ? Invoice::HAS_ERROR : Invoice::NO_ERROR;
        if ($invoice->has_error !== $hasError) {
            $invoice->has_error = $hasError;
            $invoice->save();
        }

        echo '---End calculate sale order price for seller---' . PHP_EOL;
    }

    public function updateStoreAndExportInvoice($invoice)
    {
        $invoiceRepository = resolve(InvoiceRepository::class);
        echo "status : $invoice->has_error" . PHP_EOL;
        echo "Dispatch generate new production invoice#$invoice->id" . PHP_EOL;
        $invoiceRepository->dispatchJobGenerateProductionInvoice($invoice);
        echo "Dispatch generate new shipping invoice#$invoice->id" . PHP_EOL;
        $invoiceRepository->dispatchJobGenerateShippingInvoice($invoice);
        echo "Dispatch generate new pdf invoice#$invoice->id" . PHP_EOL;
        $invoiceRepository->dispatchJobGenerateInvoice($invoice);
        echo "Dispatch generate new surcharge invoice#$invoice->id" . PHP_EOL;
        $invoiceRepository->dispatchJobGenerateSurchargeInvoice($invoice);
        echo "Dispatch generate new promotion invoice#$invoice->id" . PHP_EOL;
        $invoiceRepository->dispatchJobGeneratePromotionInvoice($invoice);
    }

    public function calculatePriceForInsertOrder($invoice, $store, $productPrintSides, $productPrintAreas, $reCalculatePrice = false)
    {
        $i = 1;
        $saleOrdersInsertsInvoice = [];
        InvoiceSaleOrderInsert::select('*')
            ->where('invoice_id', $invoice->id)
            ->orderBy('id')
            ->chunkById(100, function ($chunks) use (&$i, &$saleOrdersInsertsInvoice) {
                echo("Chunk $i has " . $chunks->count() . ' sale orders start...') . PHP_EOL;
                foreach ($chunks as $chunk) {
                    $saleOrdersInsertsInvoice[] = $chunk;
                }
                echo("Chunk $i ended.") . PHP_EOL;
                $i++;
            });

        foreach ($saleOrdersInsertsInvoice as $saleOrderInsertInvoice) {
            try {
                $orderInsertNotCalculated = SaleOrderInsertCalculatePrice::where('order_insert_id', $saleOrderInsertInvoice->order_insert_id)->first();
                if (!$reCalculatePrice && $orderInsertNotCalculated && $orderInsertNotCalculated->calculated_at) {
                    continue;
                }
                $saleOrder = SaleOrder::with([
                    'items',
                    'shipmentDefault',
                    'addressSaleOrder' => function ($queryAddress) {
                        $queryAddress->where('type_address', SaleOrderAddress::TO_ADDRESS);
                    }
                ])->where('id', $orderInsertNotCalculated->order_id)->first();

                if ($saleOrder->is_create_manual && $saleOrder->created_at == $saleOrder->calculated_at) {
                    continue;
                }
                $tag = explode(',', $saleOrder->tag);

                $addressShip = $saleOrder->addressSaleOrder[0];
                // get ship price by product type
                $destination = in_array(strtoupper($addressShip->country), StoreShipment::DOMESTIC_SHIPPING) ? StoreShipment::DOMESTIC : StoreShipment::INTERNATIONAL;
                $shippingPrice = StoreShipment::where('store_id', $saleOrder->store_id)
                    ->where('status', StoreShipment::STATUS_ACTIVE)
                    ->where('service_type', $saleOrder->shipping_method)
                    ->where('destination', $destination)
                    ->get();

                $isShipBySwiftpod = $saleOrder->shipmentDefault && (
                    $saleOrder->shipmentDefault->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD
                    || is_null($saleOrder->shipmentDefault->shipment_account)
                ) && $saleOrder->order_type !== SaleOrder::ORDER_TYPE_LABEL_ORDER && (($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && !in_array(Tag::LABEL_TAG_ID, $tag)) || $saleOrder->order_type !== SaleOrder::ORDER_TYPE_NORMAL);

                $saleOrdersInsert = SaleOrderInsert::where('order_id', $saleOrder->id)->get();
                foreach ($saleOrdersInsert as $saleOrderInsert) {
                    $mappingSku = SaleOrderInsertSku::where('size', $saleOrderInsert->size)
                        ->where('type', $saleOrderInsert->type)->first();

                    if (!$mappingSku) {
                        SaleOrderInsertCalculatePrice::updateOrCreate([
                            'order_insert_id' => $saleOrderInsert->id
                        ], [
                            'order_id' => $saleOrderInsert->order_id,
                            'size' => $saleOrderInsert->size,
                            'type' => $saleOrderInsert->type,
                            'reason' => 'Product not exists',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        continue;
                    }

                    $product = Product::with('productSize')->where('sku', $mappingSku->sku)->first();

                    if (!$product) {
                        SaleOrderInsertCalculatePrice::updateOrCreate([
                            'order_insert_id' => $saleOrderInsert->id
                        ], [
                            'order_id' => $saleOrderInsert->order_id,
                            'size' => $saleOrderInsert->size,
                            'type' => $saleOrderInsert->type,
                            'reason' => 'product sku not found',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        continue;
                    }

                    $baseStoreProduct = StoreProduct::where('store_id', $store->id)
                        ->where('product_id', $product->id)
                        ->where('product_print_area_id', 0)
                        ->first();

                    if (!$baseStoreProduct) {
                        SaleOrderInsertCalculatePrice::updateOrCreate([
                            'order_insert_id' => $saleOrderInsert->id
                        ], [
                            'order_id' => $saleOrderInsert->order_id,
                            'size' => $saleOrderInsert->size,
                            'type' => $saleOrderInsert->type,
                            'reason' => 'Base price not exists',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        continue;
                    }

                    $baseUnitPrice = $baseStoreProduct->price + $baseStoreProduct->handling_fee;
                    $printSurcharge = 0;

                    if (!$isShipBySwiftpod) {
                        $printSurcharge = $baseStoreProduct->print_surcharge ?? 0;
                    }
                    $unitPrice = $baseUnitPrice + $printSurcharge;
                    $productPrintSideName = $productPrintSides
                        ->where('code_wip', 'F') //default la front (F)
                        ->pluck('name');
                    $mapProductPrintAreas = $productPrintAreas->filter(function ($printArea) use ($productPrintSideName) {
                        return $productPrintSideName->contains($printArea->name);
                    });
                    $unitPrice += StoreProduct::where('store_id', $store->id)
                        ->where('product_id', $product->id)
                        ->whereIn('product_print_area_id', $mapProductPrintAreas->pluck('id'))
                        ->sum('print_price') ?? 0;

                    $totalShipPrice = 0;

                    if (
                        $store->is_calculate_shipping
                        && $saleOrder->order_status !== SaleOrder::STATUS_LATE_CANCELLED
                        && $isShipBySwiftpod
                    ) {
                        $productStyle = ProductStyle::where('name', $product->style)->first();
                        if (!$productStyle) {
                            SaleOrderInsertCalculatePrice::updateOrCreate([
                                'order_insert_id' => $saleOrderInsert->id
                            ], [
                                'order_id' => $saleOrderInsert->order_id,
                                'size' => $saleOrderInsert->size,
                                'type' => $saleOrderInsert->type,
                                'reason' => 'Product style not exist.',
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);

                            continue;
                        }
                        $productType = $productStyle->type;
                        $storeShipping = $shippingPrice->first(function ($item) use ($productType, $product) {
                            $check = strtolower($item->product_type) == strtolower($productType);
                            if ($item->size) {
                                $check = $check && strtolower($item->size) == strtolower($product->productSize->sku);
                            }

                            return $check;
                        });
                        if (!$storeShipping) {
                            SaleOrderInsertCalculatePrice::updateOrCreate([
                                'order_insert_id' => $saleOrderInsert->id
                            ], [
                                'order_id' => $saleOrderInsert->order_id,
                                'size' => $saleOrderInsert->size,
                                'type' => $saleOrderInsert->type,
                                'reason' => 'Shipping price has not been set yet.',
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);

                            continue;
                        }
                        $totalShipPrice += $storeShipping->price;
                    }

                    SaleOrderInsertCalculatePrice::updateOrCreate([
                        'order_insert_id' => $saleOrderInsert->id
                    ], [
                        'order_id' => $saleOrderInsert->order_id,
                        'size' => $saleOrderInsert->size,
                        'type' => $saleOrderInsert->type,
                        'qty' => 1,
                        'unit_price' => $unitPrice,
                        'blank_price' => $baseStoreProduct->price,
                        'handling_fee' => $baseStoreProduct->handling_fee,
                        'amount_paid' => $unitPrice + $totalShipPrice,
                        'product_id' => $product->id,
                        'product_sku' => $product->sku,
                        'reason' => null,
                        'calculated_at' => now(),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } catch (\Throwable $th) {
                echo $th->getMessage() . PHP_EOL;
                echo $th->getLine() . PHP_EOL;
            }
        }

        $hasError = InvoiceSaleOrderInsert::query()
            ->select('sale_order_insert_calculate_price.id')
            ->join('sale_order_insert_calculate_price', 'sale_order_insert_calculate_price.order_insert_id', '=', 'invoice_sale_order_insert.order_insert_id')
            ->where('invoice_sale_order_insert.invoice_id', $invoice->id)
            ->whereNull('sale_order_insert_calculate_price.calculated_at')
            ->exists();
        if (!$hasError) {
            $invoice->has_error_insert_invoice = Invoice::NO_ERROR;
            $invoice->save();
        }
    }

    public function updateStoreAndExportInsertInvoice($invoice)
    {
        $invoiceRepository = resolve(InvoiceRepository::class);

        return $invoiceRepository->dispatchJobGenerateInsertInvoice($invoice);
    }

    public function reCalculatePriceForOrder($invoice, $productPrintSides)
    {
        $i = 1;
        $invoiceSaleOrders = [];
        $peakShippingFee = PeakShippingFee::first();
        $tagAdditionalService = Tag::select('id', 'name')->where('is_additional_service', true)->get();
        $tagLabel = Tag::where('name', Tag::TAG_LABEL)->first();
        InvoiceSaleOrder::select('sale_order_id')
            ->where('invoice_id', $invoice->id)
            ->orderBy('sale_order_id')
            ->chunk(100, function ($chunks) use (&$i, &$invoiceSaleOrders) {
                echo("Chunk $i has " . $chunks->count() . ' sale orders start...') . PHP_EOL;
                foreach ($chunks as $chunk) {
                    $invoiceSaleOrders[] = $chunk->sale_order_id;
                }
                echo("Chunk $i ended.") . PHP_EOL;
                $i++;
            });

        foreach ($invoiceSaleOrders as $saleOrderId) {
            try {
                $saleOrder = SaleOrder::with([
                    'items',
                    'shipmentDefault',
                    'addressSaleOrder' => function ($queryAddress) {
                        $queryAddress->where('type_address', SaleOrderAddress::TO_ADDRESS);
                    }
                ])->where('id', $saleOrderId)->first();

                if (!$saleOrder) {
                    echo("Sale order id: $saleOrderId not found") . PHP_EOL;

                    continue;
                }
                if ($saleOrder->is_create_manual && $saleOrder->created_at == $saleOrder->calculated_at) {
                    echo("Sale order id: $saleOrderId reprint without invoice") . PHP_EOL;

                    continue;
                }
                echo("Update sale order id: $saleOrder->id start...") . PHP_EOL;
                echo('Sale order has: ' . $saleOrder->items->count() . ' items') . PHP_EOL;
                $calculatePriceService = new CalculatePriceForSeller();
                $calculatePriceService->setOutput(new OutputStyle(new ArgvInput(), new NullOutput()));
                $calculatePriceService->handleCalculatePriceOrderViaPricingSnapshot($saleOrder, $productPrintSides, $tagAdditionalService, $peakShippingFee, $tagLabel, false, true);
                echo("Update sale order id: $saleOrder->id done.") . PHP_EOL;
            } catch (\Throwable $th) {
                echo $th->getMessage() . PHP_EOL;
                SaleOrderCalculateFailed::updateOrCreate([
                    'order_id' => $saleOrderId,
                    'store_id' => $invoice->store_id,
                ], [
                    'failed_at' => now(),
                ]);
            }
        }
        echo '---End calculate sale order price for seller---' . PHP_EOL;
    }
}
