<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Repositories\InvoiceRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MakeProductionInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $id;

    public $timeout = 3600; // Set the timeout to 3600 seconds (60 minutes)

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(InvoiceRepository $invoiceRepository)
    {
        try {
            $invoice = Invoice::find($this->id);
            if ($invoice) {
                if (is_null($invoice->processing_production_at)) {
                    echo "Processing production invoice $this->id\n";
                    $invoice->processing_production_at = now();
                    $invoice->save();

                    $invoiceRepository->generateProductionInvoice($invoice);
                } else {
                    echo "Invoice $this->id is already being processed for production";
                }
            } else {
                echo "Wrong invoiceId: $this->id when making production invoice";
            }
        } catch (\Throwable $th) {
            if (isset($invoice)) {
                Invoice::where('id', $invoice->id)->update(['processing_production_at' => null]);
            }
            echo 'Error when making production invoice: ' . $th->getMessage();
            throw $th;
        }
    }
}
