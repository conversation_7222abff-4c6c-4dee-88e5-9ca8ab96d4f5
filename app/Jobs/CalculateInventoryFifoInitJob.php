<?php

namespace App\Jobs;

use App\Models\Inventory;
use App\Models\InventoryReference;
use App\Models\PurchaseOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateInventoryFifoInitJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const QUEUE_NAME = 'inventory-fifo-calculate-job';

    const CHUNK_LIMIT = 1000;

    public string $productId;

    public string $warehouseId;

    public string $start;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($productId, $warehouseId, $start)
    {
        $this->productId = $productId;
        $this->warehouseId = $warehouseId;
        $this->start = $start;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("CalculateInventoryFifoInitJob.handle: Processing init FIFO for product {$this->productId} and warehouse {$this->warehouseId}");

        try {
            // init data fifo
            Inventory::query()
                ->from(DB::raw('inventory USE INDEX (product_id)'))
                ->where('product_id', $this->productId)
                ->where('warehouse_id', $this->warehouseId)
                ->where('created_at', '>=', $this->start)
                ->update([
                    'stock_qty_ending' => 0,
                    'stock_value_ending' => 0,
                    'remaining_qty' => 0,
                    'cost_total' => 0,
                    'fifo_calculated_at' => Inventory::FIFO_NOT_CALCULATED,
                ]);

            // delete all inventory reference generate from job fifo calculation
            DB::table('inventory_references')
                ->select('inventory_references.id')
                ->join('inventory', 'inventory.id', '=', 'inventory_references.inventory_id')
                ->where('inventory.product_id', $this->productId)
                ->where('inventory.warehouse_id', $this->warehouseId)
                ->where('inventory.created_at', '>=', $this->start)
                ->whereIn('inventory.object_name', [
                    Inventory::OBJECT_REVERT_DEDUCTION,
                    Inventory::OBJECT_DEDUCTION,
                    Inventory::OBJECT_DEDUCTION_UNLINKED,
                    Inventory::OBJECT_DEDUCTION_SPOILAGE,
                    Inventory::OBJECT_ADJUST_PULLING_SHELVES,
                    Inventory::OBJECT_ADJUST_SHELVE_FACE,
                    Inventory::OBJECT_TEST_COUNT,
                    Inventory::OBJECT_STOCK_TRANSFER,
                    Inventory::OBJECT_INTERNAL_REQUEST,
                ])
                ->orderBy('inventory_references.id')
                ->chunkById(self::CHUNK_LIMIT, function ($items) {
                    DB::table('inventory_references')
                        ->whereIn('id', $items->pluck('id')->values())
                        ->delete();
                }, 'inventory_references.id', 'id');

            // init cost_total, remaining qty type input inventory
            $this->refreshDataAddition(['start' => $this->start]);
            $this->refreshDataAdjustPullingShelve(['start' => $this->start]);
            $this->refreshDataAdjustShelveFace(['start' => $this->start]);
            $this->refreshDataTestCount(['start' => $this->start]);

            $this->refreshDataRevertAddition();
            $this->resetObjectNameDeduction();
            $this->refreshDataRevertDeduction();

            $this->refreshInventoryOld();
        } catch (\Throwable $e) {
            Log::error('CalculateInventoryFifoInitJob.handle: FIFO calculation failed', [
                'product_id' => $this->productId,
                'warehouse_id' => $this->warehouseId,
                'exception' => $e,
            ]);
        }
    }

    public function refreshDataAddition(array $params = [], array $appendDataUpdate = []): void
    {
        $query = DB::table('inventory')
            ->selectRaw('
                inventory.id,
                COALESCE(purchase_order_item.price * inventory.quantity, 0) as `cost_total`
            ')
            ->join('inventory_addition', function ($join) {
                $join->on('inventory.object_id', '=', 'inventory_addition.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_ADDITION);
            })
            ->leftJoin('purchase_order_item', function ($join) {
                $join->on('purchase_order_item.po_id', '=', 'inventory_addition.po_id')
                    ->on('purchase_order_item.product_id', '=', 'inventory_addition.product_id');
            })
            ->where('inventory.product_id', $this->productId)
            ->where('inventory.warehouse_id', $this->warehouseId)
            ->where('inventory.direction', Inventory::DIRECTION_INPUT);

        if (!empty($params['start'])) {
            $query->where('inventory.created_at', '>=', $params['start']);
        }

        if (!empty($params['inventoryId'])) {
            $query->where('inventory.id', $params['inventoryId']);
        }

        $query->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) use ($appendDataUpdate) {
                foreach ($inventories as $item) {
                    $dataUpdate = ['cost_total' => $item->cost_total];
                    DB::table('inventory')->where('id', $item->id)
                        ->update(array_merge($dataUpdate, $appendDataUpdate));
                }
            }, 'inventory.id', 'id');
    }

    public function refreshDataRevertAddition(): void
    {
        $start = $this->start;
        $productId = $this->productId;
        $warehouseId = $this->warehouseId;

        DB::table('inventory')
            ->select(['inventory.id', 'i2.cost_total'])
            ->join('inventory as i2', 'inventory.replicated_from_id', '=', 'i2.id')
            ->where('inventory.product_id', $productId)
            ->where('inventory.warehouse_id', $warehouseId)
            ->where('inventory.created_at', '>=', $start)
            ->where('inventory.object_name', Inventory::OBJECT_REVERT_ADDITION)
            ->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) {
                foreach ($inventories as $item) {
                    DB::table('inventory')->where('id', $item->id)
                        ->update(['cost_total' => $item->cost_total]);
                }
            }, 'inventory.id', 'id');

        $inventoryAdditionRevert = Inventory::query()
            ->from(DB::raw('inventory USE INDEX (product_id)'))
            ->where('inventory.product_id', $productId)
            ->where('inventory.warehouse_id', $warehouseId)
            ->where('inventory.created_at', '>=', $start)
            ->where('inventory.object_name', Inventory::OBJECT_ADDITION)
            ->whereNotExists(function ($query) use ($productId, $warehouseId, $start) {
                $query->select(DB::raw(1))
                    ->from('inventory as i2')
                    ->whereColumn('i2.replicated_from_id', 'inventory.id')
                    ->where('i2.product_id', $productId)
                    ->where('i2.warehouse_id', $warehouseId)
                    ->where('i2.created_at', '>=', $start)
                    ->whereNotNull('i2.replicated_from_id');
            })
            ->where('inventory.is_deleted', 1)
            ->get();

        foreach ($inventoryAdditionRevert as $item) {
            $newInventory = $item->replicate();
            $newInventory->direction = Inventory::DIRECTION_OUTPUT;
            $newInventory->type = Inventory::TYPE_REVERT;
            $newInventory->object_name = Inventory::OBJECT_REVERT_ADDITION;
            $newInventory->fifo_calculated_at = Inventory::FIFO_NOT_CALCULATED;
            $newInventory->stock_qty_ending = 0;
            $newInventory->stock_value_ending = 0;
            $newInventory->remaining_qty = 0;
            $newInventory->created_at = $item->created_at;
            $newInventory->replicated_from_id = $item->id;
            $newInventory->is_deleted = 1;
            $newInventory->save();

            $item->remaining_qty = 0;
            $item->save();

            InventoryReference::query()->create([
                'inventory_id' => $newInventory->id,
                'referenced_inventory_id' => $item->id,
                'quantity' => $item->quantity,
                'cost_total' => $item->cost_total,
            ]);
        }
    }

    public function refreshDataRevertDeduction(): void
    {
        $start = $this->start;
        $productId = $this->productId;
        $warehouseId = $this->warehouseId;

        $inventoryRevert = Inventory::query()
            ->from(DB::raw('inventory USE INDEX (product_id)'))
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('created_at', '>=', $start)
            ->whereIn('object_name', [
                Inventory::OBJECT_DEDUCTION,
                Inventory::OBJECT_DEDUCTION_SPOILAGE,
                Inventory::OBJECT_DEDUCTION_UNLINKED,
            ])
            ->whereNotExists(function ($query) use ($productId, $warehouseId, $start) {
                $query->select(DB::raw(1))
                    ->from('inventory as i2')
                    ->whereColumn('i2.replicated_from_id', 'inventory.id')
                    ->where('i2.product_id', $productId)
                    ->where('i2.warehouse_id', $warehouseId)
                    ->where('i2.created_at', '>=', $start)
                    ->whereNotNull('i2.replicated_from_id');
            })
            ->where('is_deleted', 1)
            ->get();

        foreach ($inventoryRevert as $item) {
            $newInventory = $item->replicate();
            $newInventory->direction = Inventory::DIRECTION_INPUT;
            $newInventory->type = Inventory::TYPE_REVERT;
            $newInventory->object_name = Inventory::OBJECT_REVERT_DEDUCTION;
            $newInventory->fifo_calculated_at = Inventory::FIFO_NOT_CALCULATED;
            $newInventory->stock_qty_ending = 0;
            $newInventory->stock_value_ending = 0;
            $newInventory->remaining_qty = 0;
            $newInventory->cost_total = 0;
            $newInventory->created_at = $item->created_at;
            $newInventory->replicated_from_id = $item->id;
            $newInventory->is_deleted = 1;
            $newInventory->save();
        }
    }

    public function refreshDataAdjustPullingShelve(array $params = [], array $appendDataUpdate = []): void
    {
        $queryUpdateCostTotal = DB::raw("COALESCE((
                    SELECT purchase_order_item.price * inventory.quantity
                    FROM purchase_order
                    JOIN purchase_order_item ON purchase_order.id = purchase_order_item.po_id
                    WHERE purchase_order_item.product_id = inventory.product_id
                        AND purchase_order.order_date <= DATE_FORMAT(adjust_pulling_shelves.created_at, '%Y-%m-%d')
                        AND purchase_order.order_status <> '" . PurchaseOrder::CANCELLED_STATUS . "'
                        AND purchase_order_item.price > 0
                    ORDER BY purchase_order.order_date DESC, purchase_order.id DESC
                    LIMIT 1
                ), 0) as cost_total");
        $query = DB::table('inventory')
            ->select([
                'inventory.id',
                $queryUpdateCostTotal,
            ])
            ->join('adjust_pulling_shelves', function ($join) {
                $join->on('inventory.object_id', '=', 'adjust_pulling_shelves.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_ADJUST_PULLING_SHELVES);
            })
            ->where('inventory.product_id', $this->productId)
            ->where('inventory.warehouse_id', $this->warehouseId)
            ->where('inventory.direction', Inventory::DIRECTION_INPUT);

        if (!empty($params['start'])) {
            $query->where('inventory.created_at', '>=', $params['start']);
        }

        if (!empty($params['inventoryId'])) {
            $query->where('inventory.id', $params['inventoryId']);
        }

        $query->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) use ($appendDataUpdate) {
                foreach ($inventories as $item) {
                    $dataUpdate = ['cost_total' => $item->cost_total];
                    DB::table('inventory')->where('id', $item->id)
                        ->update(array_merge($dataUpdate, $appendDataUpdate));
                }
            }, 'inventory.id', 'id');
    }

    public function refreshDataAdjustShelveFace(array $params = [], array $appendDataUpdate = []): void
    {
        $queryUpdateCostTotal = DB::raw("COALESCE((
                    SELECT purchase_order_item.price * inventory.quantity
                    FROM purchase_order
                    JOIN purchase_order_item ON purchase_order.id = purchase_order_item.po_id
                    WHERE purchase_order_item.product_id = inventory.product_id
                        AND purchase_order.order_date <= DATE_FORMAT(adjust_shelves_face.created_at, '%Y-%m-%d')
                        AND purchase_order.order_status <> '" . PurchaseOrder::CANCELLED_STATUS . "'
                        AND purchase_order_item.price > 0
                    ORDER BY purchase_order.order_date DESC, purchase_order.id DESC
                    LIMIT 1
                ), 0) as cost_total");
        $query = DB::table('inventory')
            ->select([
                'inventory.id',
                $queryUpdateCostTotal,
            ])
            ->join('adjust_shelves_face', function ($join) {
                $join->on('inventory.object_id', '=', 'adjust_shelves_face.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_ADJUST_SHELVE_FACE);
            })
            ->where('inventory.product_id', $this->productId)
            ->where('inventory.warehouse_id', $this->warehouseId)
            ->where('inventory.direction', Inventory::DIRECTION_INPUT);

        if (!empty($params['start'])) {
            $query->where('inventory.created_at', '>=', $params['start']);
        }

        if (!empty($params['inventoryId'])) {
            $query->where('inventory.id', $params['inventoryId']);
        }

        $query->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) use ($appendDataUpdate) {
                foreach ($inventories as $item) {
                    $dataUpdate = ['cost_total' => $item->cost_total];
                    DB::table('inventory')->where('id', $item->id)
                        ->update(array_merge($dataUpdate, $appendDataUpdate));
                }
            }, 'inventory.id', 'id');
    }

    public function refreshDataTestCount(array $params = [], array $appendDataUpdate = []): void
    {
        $query = DB::table('inventory')
            ->select([
                'inventory.id',
                'box.cost_value',
            ])
            ->join('box', 'box.id', '=', 'inventory.box_id')
            ->where('inventory.object_name', Inventory::OBJECT_TEST_COUNT)
            ->where('inventory.product_id', $this->productId)
            ->where('inventory.warehouse_id', $this->warehouseId)
            ->where('inventory.direction', Inventory::DIRECTION_INPUT);

        if (!empty($params['start'])) {
            $query->where('inventory.created_at', '>=', $params['start']);
        }

        if (!empty($params['inventoryId'])) {
            $query->where('inventory.id', $params['inventoryId']);
        }

        $query->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) use ($appendDataUpdate) {
                foreach ($inventories as $item) {
                    $dataUpdate = ['cost_total' => $item->cost_value];
                    DB::table('inventory')->where('id', $item->id)
                        ->update(array_merge($dataUpdate, $appendDataUpdate));
                }
            }, 'inventory.id', 'id');
    }

    public function refreshDataCreateBox(array $params = [], array $appendDataUpdate = []): void
    {
        $queryUpdateCostTotal = DB::raw("COALESCE((
                    SELECT purchase_order_item.price * inventory.quantity
                    FROM purchase_order
                    JOIN purchase_order_item ON purchase_order.id = purchase_order_item.po_id
                    WHERE purchase_order_item.product_id = inventory.product_id
                        AND purchase_order.order_date <= DATE_FORMAT(inventory.created_at, '%Y-%m-%d')
                        AND purchase_order.order_status <> '" . PurchaseOrder::CANCELLED_STATUS . "'
                        AND purchase_order_item.price > 0
                    ORDER BY purchase_order.order_date DESC, purchase_order.id DESC
                    LIMIT 1
                ), 0) as cost_total");
        $query = DB::table('inventory')
            ->select([
                'inventory.id',
                $queryUpdateCostTotal,
            ])
            ->where('inventory.object_name', Inventory::OBJECT_CREATE_BOX)
            ->where('inventory.product_id', $this->productId)
            ->where('inventory.warehouse_id', $this->warehouseId)
            ->where('inventory.direction', Inventory::DIRECTION_INPUT);

        if (!empty($params['start'])) {
            $query->where('inventory.created_at', '>=', $params['start']);
        }

        if (!empty($params['inventoryId'])) {
            $query->where('inventory.id', $params['inventoryId']);
        }

        $query->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) use ($appendDataUpdate) {
                foreach ($inventories as $item) {
                    $dataUpdate = ['cost_total' => $item->cost_total];
                    DB::table('inventory')->where('id', $item->id)
                        ->update(array_merge($dataUpdate, $appendDataUpdate));
                }
            }, 'inventory.id', 'id');
    }

    public function refreshInventoryOld(): void
    {
        Inventory::query()
            ->from(DB::raw('inventory USE INDEX (product_id)'))
            ->where('product_id', $this->productId)
            ->where('warehouse_id', $this->warehouseId)
            ->where('created_at', '<', $this->start)
            ->update(
                [
                    'stock_qty_ending' => 0,
                    'stock_value_ending' => 0,
                    'remaining_qty' => 0,
                    'fifo_calculated_at' => now('UTC')->format('YmdHis'),
                ]);

        $stockFifo = Inventory::query()
            ->from(DB::raw('inventory USE INDEX (product_id)'))
            ->select('product_id', 'warehouse_id', DB::raw('
                SUM(
                    CASE
                        WHEN direction = ' . Inventory::DIRECTION_INPUT . ' THEN quantity
                        WHEN direction = ' . Inventory::DIRECTION_OUTPUT . ' THEN -quantity
                    END
                ) AS ending_stock
            '))
            ->where('is_deleted', 0)
            ->whereNotIn('object_name', [Inventory::OBJECT_REVERT_ADDITION, Inventory::OBJECT_REVERT_DEDUCTION])
            ->where('product_id', $this->productId)
            ->where('warehouse_id', $this->warehouseId)
            ->where('created_at', '<', $this->start)
            ->groupBy('product_id')
            ->first();

        if (!empty($stockFifo->ending_stock)) {
            $fistFifo = Inventory::query()
                ->from(DB::raw('inventory USE INDEX (product_id)'))
                ->where('product_id', $this->productId)
                ->where('warehouse_id', $this->warehouseId)
                ->where('created_at', '<', $this->start)
                ->orderByDesc('created_at')
                ->orderByDesc('id')
                ->first();
            $stockQty = $stockFifo->ending_stock;
            $stockQtyEndingCalc = 0;
            $stockValueEnding = 0;

            if ($stockQty > 0) {
                while ($stockQty > 0) {
                    $inventoryInput = Inventory::query()
                        ->from(DB::raw('inventory USE INDEX (product_id)'))
                        ->select(['id', 'quantity', 'object_name'])
                        ->where('product_id', $this->productId)
                        ->where('warehouse_id', $this->warehouseId)
                        ->where('created_at', '<', $this->start)
                        ->where('direction', Inventory::DIRECTION_INPUT)
                        ->where('remaining_qty', 0)
                        ->where('is_deleted', 0)
                        ->orderByDesc('created_at')
                        ->orderByDesc('id')
                        ->limit(500)
                        ->get();

                    if (count($inventoryInput) == 0) {
                        break;
                    }

                    foreach ($inventoryInput as $item) {
                        if ($stockQty <= 0) {
                            break 2;
                        }

                        $remainingQty = min($stockQty, $item->quantity);
                        $stockQty -= $remainingQty;
                        $params = ['inventoryId' => $item->id];
                        $appendDataUpdate = ['remaining_qty' => $remainingQty];

                        switch ($item->object_name) {
                            case Inventory::OBJECT_ADDITION:
                                $this->refreshDataAddition($params, $appendDataUpdate);
                                break;

                            case Inventory::OBJECT_ADJUST_SHELVE_FACE:
                                $this->refreshDataAdjustShelveFace($params, $appendDataUpdate);
                                break;

                            case Inventory::OBJECT_ADJUST_PULLING_SHELVES:
                                $this->refreshDataAdjustPullingShelve($params, $appendDataUpdate);
                                break;

                            case Inventory::OBJECT_TEST_COUNT:
                                $this->refreshDataTestCount($params, $appendDataUpdate);
                                break;

                            case Inventory::OBJECT_CREATE_BOX:
                                $this->refreshDataCreateBox($params, $appendDataUpdate);
                                break;
                        }

                        $inventoryRefresh = Inventory::query()
                            ->select(['quantity', 'cost_total', 'remaining_qty'])
                            ->find($item->id);
                        $remainingQty = $inventoryRefresh->remaining_qty ?? 0;
                        $stockQtyEndingCalc += $remainingQty;
                        $costTotal = $inventoryRefresh->cost_total ?? 0;
                        $qty = $inventoryRefresh->quantity ?? 0;
                        $stockValueEnding += empty($qty) ? 0 : ($costTotal / $qty * $remainingQty);
                    }
                }
            } else {
                $stockQtyEndingCalc = $stockQty;
            }

            $fistFifo->stock_qty_ending = $stockQtyEndingCalc;
            $fistFifo->stock_value_ending = $stockValueEnding;
            $fistFifo->save();
        }
    }

    public function resetObjectNameDeduction(): void
    {
        DB::table('inventory')
            ->select(['inventory.id'])
            ->join('inventory_deduction', function ($join) {
                $join->on('inventory.object_id', '=', 'inventory_deduction.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_DEDUCTION);
            })
            ->where('inventory_deduction.is_duplicate', 1)
            ->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) {
                foreach ($inventories as $item) {
                    DB::table('inventory')
                        ->where('id', $item->id)
                        ->update(['object_name' => Inventory::OBJECT_DEDUCTION_SPOILAGE]);
                }
            }, 'inventory.id', 'id');

        DB::table('inventory')
            ->select(['inventory.id'])
            ->join('inventory_deduction', function ($join) {
                $join->on('inventory.object_id', '=', 'inventory_deduction.id')
                    ->where('inventory.object_name', '=', Inventory::OBJECT_DEDUCTION);
            })
            ->whereNull('inventory_deduction.sale_order_id')
            ->orderBy('inventory.id')
            ->chunkById(self::CHUNK_LIMIT, function ($inventories) {
                foreach ($inventories as $item) {
                    DB::table('inventory')
                        ->where('id', $item->id)
                        ->update(['object_name' => Inventory::OBJECT_DEDUCTION_UNLINKED]);
                }
            }, 'inventory.id', 'id');
    }
}
