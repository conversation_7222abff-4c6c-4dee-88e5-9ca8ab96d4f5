<?php

namespace App\Jobs\QuickBook;

use App\Models\QuickBookCompany;
use App\Models\QuickBookSyncHistory;
use App\Models\Store;
use App\Services\QuickBookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use QuickBooksOnline\API\Exception\ServiceException;

class CreateCustomerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string $companyId,
        protected int $storeId,
    ) {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QuickBookService $quickBookService)
    {
        try {
            $store = Store::with([
                'quickBookCustomers',
                'walletBillingAddress'
            ])->find($this->storeId);

            if ($store->quickBookCustomers($this->companyId)->exists()) {
                return;
            }

            $history = QuickBookSyncHistory::create([
                'company_id' => $this->companyId,
                'spa_id' => $this->storeId,
                'spa_model' => Store::class,
                'qb_ref_type' => QuickBookCompany::ENTITY_TYPE_CUSTOMER,
                'action' => QuickBookSyncHistory::ACTION_CREATE,
                'status' => QuickBookSyncHistory::STATUS_PENDING,
            ]);

            /** @var CustomerService $customerService */
            $customerService = $quickBookService->getService($this->companyId, QuickBookCompany::ENTITY_TYPE_CUSTOMER);
            
            $billingAddress = $store->billingAddress();
            $createCustomerPayload = [
                "DisplayName" => $store->code . ' - ' . $store->name,
                "CompanyName" => $store?->company,
                "PrimaryPhone" => [
                    "FreeFormNumber" => $store?->phone
                ],
                "PrimaryEmailAddr" => [
                    "Address" => $billingAddress['email']
                ],
                "BillAddr" => [
                    "Line1" => $billingAddress['street1'],
                    "City" => $billingAddress['city'],
                    "CountrySubDivisionCode" => $billingAddress['state'],
                    "PostalCode" => $billingAddress['zip'],
                    "Country" => $billingAddress['country'],
                ],
                "CustomField" => [
                    [
                        "DefinitionId" => "1",
                        "Name" => "Store ID",
                        "Type" => "StringType",
                        "StringValue" => (string)$store->id
                    ]
                ]
            ];

            $customer = $customerService->createCustomer($createCustomerPayload);

            $store->quickBookCustomers()->create([
                'company_id' => $this->companyId,
                'model_type' => Store::class,
                'qb_ref_id' => $customer->Id,
                'qb_ref_type' => QuickBookCompany::ENTITY_TYPE_CUSTOMER,
            ]);

            $history->update([
                'qb_ref_id' => $customer->Id,
                'status' => QuickBookSyncHistory::STATUS_SUCCESS,
            ]);
        } catch (\Throwable $th) {
            $message = $th instanceof ServiceException ? $th->__toString() : $th->getMessage();

            if (isset($history)) {
                $history->update([
                    'status' => QuickBookSyncHistory::STATUS_FAILED,
                    'error_message' => $message,
                ]);
            }

            throw $th;
        }
    }
}
