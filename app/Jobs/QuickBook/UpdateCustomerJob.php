<?php

namespace App\Jobs\QuickBook;

use App\Models\QuickBookCompany;
use App\Models\QuickBookSyncHistory;
use App\Models\Store;
use App\Services\QuickBookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use QuickBooksOnline\API\Exception\ServiceException;

class UpdateCustomerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string $companyId,
        protected int $storeId,
    ) {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QuickBookService $quickBookService)
    {
        try {
            $history = QuickBookSyncHistory::create([
                'company_id' => $this->companyId,
                'spa_id' => $this->storeId,
                'spa_model' => Store::class,
                'qb_ref_type' => QuickBookCompany::ENTITY_TYPE_CUSTOMER,
                'action' => QuickBookSyncHistory::ACTION_UPDATE,
                'status' => QuickBookSyncHistory::STATUS_PENDING,
            ]);

            $store = Store::with([
                'quickBookCustomers',
                'walletBillingAddress'
            ])->find($this->storeId);

            $internalCustomer = $store->quickBookCustomers($this->companyId)->first();
            $billingAddress = $store->billingAddress();

            /** @var CustomerService $customerService */
            $customerService = $quickBookService->getService($this->companyId, QuickBookCompany::ENTITY_TYPE_CUSTOMER);

            $createCustomerPayload = [
                "PrimaryPhone" => [
                    "FreeFormNumber" => $store?->phone
                ],
                "PrimaryEmailAddr" => [
                    "Address" => $store?->email
                ],
                "BillAddr" => [
                    "Line1" => $billingAddress['street1'],
                    "City" => $billingAddress['city'],
                    "CountrySubDivisionCode" => $billingAddress['state'],
                    "PostalCode" => $billingAddress['zip'],
                    "Country" => $billingAddress['country'],
                ],
                "BillEmail" => [
                    "Address" => $billingAddress['email']
                ],
            ];

            $customerService->updateCustomer($internalCustomer->qb_ref_id, $createCustomerPayload);

            $history->update([
                'qb_ref_id' => $internalCustomer->qb_ref_id,
                'status' => QuickBookSyncHistory::STATUS_SUCCESS,
            ]);
        } catch (\Throwable $th) {
            $message = $th instanceof ServiceException ? $th->__toString() : $th->getMessage();

            if (isset($history)) {
                $history->update([
                    'status' => QuickBookSyncHistory::STATUS_FAILED,
                    'error_message' => $message,
                ]);
            }

            throw $th;
        }
    }
}
