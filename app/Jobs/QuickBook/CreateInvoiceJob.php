<?php

namespace App\Jobs\QuickBook;

use App\Models\Invoice;
use App\Models\QuickBookCompany;
use App\Models\QuickBookSyncHistory;
use App\Repositories\InvoiceRepository;
use App\Services\QuickBookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use QuickBooksOnline\API\Exception\ServiceException;

class CreateInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string $companyId,
        protected int $invoiceId,
    ) {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QuickBookService $quickBookService, InvoiceRepository $invoiceRepository)
    {
        try {
            $history = QuickBookSyncHistory::create([
                'company_id' => $this->companyId,
                'spa_id' => $this->invoiceId,
                'spa_model' => Invoice::class,
                'qb_ref_type' => QuickBookCompany::ENTITY_TYPE_INVOICE,
                'action' => QuickBookSyncHistory::ACTION_CREATE,
                'status' => QuickBookSyncHistory::STATUS_PENDING,
            ]);

            $internalInvoice = Invoice::with(['store'])->find($this->invoiceId);

            /** @var InvoiceService $invoiceService */
            $invoiceService = $quickBookService->getService($this->companyId, QuickBookCompany::ENTITY_TYPE_INVOICE);

            /** @var CustomerService $customerService */
            $customerService = $quickBookService->getService($this->companyId, QuickBookCompany::ENTITY_TYPE_CUSTOMER);


            /** Create Invoice */
            $quickBookMapping = $internalInvoice->store->quickBookCustomers($this->companyId)->first();
            $quickBookCustomer = $customerService->findCustomerById($quickBookMapping->qb_ref_id);

            $internalLineItems = $invoiceRepository->getLineItemsByInvoiceId($this->invoiceId);
            $fileInvoices = $invoiceRepository->getFilesByInvoiceId($this->invoiceId);

            $billingEmail = $quickBookCustomer->PrimaryEmailAddr->Address ?? null;
            $payloadCreateInvoice = [
                "Line" => $invoiceService->getInvoiceLineItems($internalLineItems),
                "TxnDate" => $internalInvoice->end_at->toFormattedDateString(),
                "DueDate" => $internalInvoice->created_at->addDays($internalInvoice->store->days_until_bill_due)->toFormattedDateString(),
                "CustomerRef"=> [
                    "value"=> $quickBookMapping->qb_ref_id,
                ]
            ];

            if ($billingEmail) {
                $payloadCreateInvoice['EmailStatus'] = 'NeedToSend';
                $payloadCreateInvoice['BillEmail'] = [
                    "Address" => $billingEmail
                ];
            }

            $history->update([
                'data' => [
                    'internal_line_items' => $internalLineItems,
                    'quick_book_payload' => $payloadCreateInvoice,
                    'file_invoices' => $fileInvoices,
                ]
            ]);

            $invoice = $invoiceService->createInvoice($payloadCreateInvoice);

            /** Attach File */
            $invoiceService->uploadFile($fileInvoices, $invoice->Id);

            $internalInvoice->quickBookInvoices()->create([
                'company_id' => $this->companyId,
                'model_type' => Invoice::class,
                'qb_ref_id' => $invoice->Id,
                'qb_ref_type' => QuickBookCompany::ENTITY_TYPE_INVOICE,
            ]);

            $history->update([
                'qb_ref_id' => $invoice->Id,
                'status' => QuickBookSyncHistory::STATUS_SUCCESS
            ]);
        } catch (\Throwable $th) {
            $message = $th instanceof ServiceException ? $th->__toString() : $th->getMessage();

            if (isset($history)) {
                $history->update([
                    'status' => QuickBookSyncHistory::STATUS_FAILED,
                    'error_message' => $message,
                ]);
            }

            throw $th;
        }
    }
}
