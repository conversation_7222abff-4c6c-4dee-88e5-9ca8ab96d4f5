<?php

namespace App\Jobs\QuickBook;

use App\Models\Invoice;
use App\Models\QuickBookCompany;
use App\Models\QuickBookSyncHistory;
use App\Services\QuickBookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\QuickBook\InvoiceService;
use QuickBooksOnline\API\Exception\ServiceException;

class SendInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string $companyId,
        protected int $invoiceId,
    ) {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QuickBookService $quickBookService)
    {
        try {
            $history = QuickBookSyncHistory::create([
                'company_id' => $this->companyId,
                'spa_id' => $this->invoiceId,
                'spa_model' => Invoice::class,
                'qb_ref_type' => QuickBookCompany::ENTITY_TYPE_INVOICE,
                'action' => QuickBookSyncHistory::ACTION_SEND_INVOICE,
                'status' => QuickBookSyncHistory::STATUS_PENDING,
            ]);

            $internalInvoice = Invoice::with(['store'])->find($this->invoiceId);

            /** @var InvoiceService $invoiceService */
            $invoiceService = $quickBookService->getService($this->companyId, QuickBookCompany::ENTITY_TYPE_INVOICE);
            $quickBookInvoiceId = $internalInvoice->quickBookInvoices($this->companyId)->first()->qb_ref_id;
            $invoice = $invoiceService->findInvoiceById($quickBookInvoiceId);

            $billingAddress = $internalInvoice->store->billingAddress();

            $invoiceService->sendInvoice($invoice, $billingAddress['email']);

            $history->update([
                'status' => QuickBookSyncHistory::STATUS_SUCCESS,
                'qb_ref_id' => $invoice->Id,
            ]);

        } catch (\Throwable $th) {
            $message = $th instanceof ServiceException ? $th->__toString() : $th->getMessage();

            if (isset($history)) {
                $history->update([
                    'status' => QuickBookSyncHistory::STATUS_FAILED,
                    'error_message' => $message,
                ]);
            }

            throw $th;
        }
    }
}
