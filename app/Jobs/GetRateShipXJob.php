<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\Shipment;
use App\Models\StoreSetting;
use App\Repositories\LabelRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class GetRateShipXJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shipmentId;

    const URL_SHIPX_RATE = 'https://shipx-api.swiftpod.com/v1/shipments';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shipmentId)
    {
        $this->shipmentId = $shipmentId;

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $shipment = Shipment::where('id', $this->shipmentId)->first();
            if (!$shipment) {
                throw new \Exception('Shipment not found!');
            }
            $storeSetting = StoreSetting::where('store_id', $shipment->store_id)
                            ->where('key', StoreSetting::KEY_SHIPX_API_KEY)
                            ->first();
            if (!$storeSetting) {
                throw new \Exception('Store setting not found!');
            }

            // Debug token
            \Log::info('ShipX Token Debug', [
                'store_id' => $shipment->store_id,
                'token_prefix' => substr($storeSetting->value, 0, 10) . '...',
                'token_length' => strlen($storeSetting->value)
            ]);
            $dataSaleOrder = SaleOrder::with(
                'store',
                'warehouse',
                'addressSaleOrder',
                'items.product',
            )
                ->with(['storeAddress' => function ($q) {
                    $q->where('type_address', '=', 'return_address');
                }])
            ->where('id', $shipment->order_id)
            ->first();
            if (!$dataSaleOrder) {
                throw new \Exception("Can't find sale order!");
            }
            $labelRepository = new LabelRepository();
            $addressTo = $labelRepository->dataAddressTo($dataSaleOrder->addressSaleOrder, false, $dataSaleOrder);
            if (strtoupper($addressTo['country']) !== 'US') {
                $customsInfo = $labelRepository->dataCustomsInfoV2($dataSaleOrder);
            } else {
                $customsInfo = in_array($addressTo['city'], SaleOrderAddress::IN_US_NEED_CUSTOMS) || in_array($addressTo['state'], SaleOrderAddress::IN_US_NEED_CUSTOMS_FOR_STATE) ? $this->dataCustomsInfoV2($dataSaleOrder) : '';
            }
            $addressFrom = $labelRepository->dataAddressFrom($dataSaleOrder->warehouse, $dataSaleOrder->store?->name, 'USPS');
            $addressReturn = $labelRepository->dataAddressReturn($dataSaleOrder->storeAddress, $dataSaleOrder->merchant_name, $dataSaleOrder->addressSaleOrder);
            $dataParcel = [
                'length' => $shipment->length,
                'width' => $shipment->width,
                'height' => $shipment->height,
                'weight' => $shipment->weight,
            ];
            // Simplified data structure like Postman
            $data = [
                'reference' => 'ref-' . $shipment->id,
                'from_address' => $addressFrom,
                'to_address' => $addressTo,
                'parcel' => $dataParcel,
            ];

            // Add optional fields if needed
            // if (!empty($customsInfo)) {
            //     $data['customs_info'] = $customsInfo;
            // }
            // if (!empty($addressReturn)) {
            //     $data['return_address'] = $addressReturn;
            // }
            // if (!empty($dataSaleOrder->ioss_number)) {
            //     $data['tax_identifiers'] = [
            //         [
            //             'entity' => 'SENDER',
            //             'tax_id' => $dataSaleOrder->ioss_number,
            //             'tax_id_type' => $dataSaleOrder->tax_id_type,
            //             'issuing_country' => $addressTo['country'],
            //         ]
            //     ];
            // }
            // Debug before calling sendRequestShipX
            \Log::info('Before sendRequestShipX call', [
                'url_constant' => self::URL_SHIPX_RATE,
                'data_structure' => $data,
                'token_exists' => !empty($storeSetting->value),
                'token_length' => strlen($storeSetting->value ?? '')
            ]);

            // Test direct HTTP call to debug
            $testResponse = Http::timeout(60)->withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $storeSetting->value,
            ])->post(self::URL_SHIPX_RATE, $data);

            \Log::info('Direct HTTP call result', [
                'status' => $testResponse->status(),
                'body' => $testResponse->body()
            ]);

            $response = $this->sendRequestShipX(self::URL_SHIPX_RATE, $data, $storeSetting->value);
            dd($response);
            if ($response->status() != 200) {
                throw new \Exception('ShipX error: ' . $response->body());
            }
            $data = $response->json();
            dd($data);

            if (isset($data['error'])) {
                throw new \Exception('ShipX error: ' . $data['error']);
            }

            $dataResRate = $data['rates'];
            if (count($dataResRate) == 0) {
                throw new \Exception('ShipX error: No rate found!');
            }

            $rate = 0;
            foreach ($dataResRate as $item) {
                if ($item['service'] == $shipment->service_code) {
                    $rate = $item['rate'];
                    break;
                }
            }

            if ($rate == 0) {
                throw new \Exception('ShipX error: not found rate!');
            }

            $shipment->update([
                'shipx_rate' => $rate,
            ]);
        } catch (\Exception $e) {
            // bắn ra google chat để biết được thông tin lỗi tránh ảnh hưởng cho invoice



        }
    }

    protected function sendRequestShipX($url, $data, $token)
    {
        // Debug logging
        \Log::info('ShipX Request Debug', [
            'url' => $url,
            'token_prefix' => substr($token, 0, 10) . '...',
            'data_keys' => array_keys($data),
            'data' => $data
        ]);

        $response = Http::timeout(60)->withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ])->post(trim($url), $data);

        \Log::info('ShipX Response Debug', [
            'status' => $response->status(),
            'headers' => $response->headers(),
            'body' => $response->body()
        ]);

        return $response;
    }
}
