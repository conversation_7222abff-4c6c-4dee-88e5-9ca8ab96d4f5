<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\StoreSetting;
use App\Repositories\LabelRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class GetRateShipXJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shipmentId;

    const URL_SHIPX_RATE = 'https://shipx-api.swiftpod.com/v1/shipments';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shipmentId)
    {
        $this->shipmentId = $shipmentId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $shipment = Shipment::where('id', $this->shipmentId)->first();
            if (!$shipment) {
                throw new \Exception('Shipment not found!' . ' - ' . 'for shipment id: ' . $this->shipmentId);
            }
            $storeSetting = StoreSetting::where('store_id', $shipment->store_id)
                            ->where('key', StoreSetting::KEY_SHIPX_API_KEY)
                            ->first();
            if (!$storeSetting) {
                throw new \Exception('Store setting not found! ' . ' - ' . 'for shipment id: ' . $shipment->id);
            }
            $dataSaleOrder = SaleOrder::with(
                'store',
                'warehouse',
                'addressSaleOrder',
                'items.product',
            )
                ->with(['storeAddress' => function ($q) {
                    $q->where('type_address', '=', 'return_address');
                }])
            ->where('id', $shipment->order_id)
            ->first();
            if (!$dataSaleOrder) {
                throw new \Exception("Can't find sale order!" . ' - ' . 'for shipment id: ' . $shipment->id . 'order number: ' . $dataSaleOrder->order_number);
            }
            $labelRepository = new LabelRepository();
            $addressTo = $labelRepository->dataAddressTo($dataSaleOrder->addressSaleOrder, false, $dataSaleOrder);
            if (strtoupper($addressTo['country']) !== 'US') {
                $customsInfo = $labelRepository->dataCustomsInfoV2($dataSaleOrder);
            } else {
                $customsInfo = in_array($addressTo['city'], SaleOrderAddress::IN_US_NEED_CUSTOMS) || in_array($addressTo['state'], SaleOrderAddress::IN_US_NEED_CUSTOMS_FOR_STATE) ? $this->dataCustomsInfoV2($dataSaleOrder) : '';
            }
            $addressFrom = $labelRepository->dataAddressFrom($dataSaleOrder->warehouse, $dataSaleOrder->store?->name, 'USPS');
            $addressReturn = $labelRepository->dataAddressReturn($dataSaleOrder->storeAddress, $dataSaleOrder->merchant_name, $dataSaleOrder->addressSaleOrder);
            $dataParcel = [
                'length' => $shipment->dimension_length ?? 9,
                'width' => $shipment->dimension_width ?? 6,
                'height' => $shipment->dimension_height ?? 2,
                'weight' => $shipment->weight_value ?? 0,
            ];
            $data = [
                'from_address' => $addressFrom,
                'to_address' => $addressTo,
                'return_address' => $addressReturn,
                'parcel' => $dataParcel,
                'customs_info' => $customsInfo,
            ];
            if (!empty($dataSaleOrder->ioss_number)) {
                $data['tax_identifiers'] = [
                    [
                        'entity' => 'SENDER',
                        'tax_id' => $dataSaleOrder->ioss_number,
                        'tax_id_type' => $dataSaleOrder->tax_id_type,
                        'issuing_country' => $addressTo['country'],
                    ]
                ];
            }
            $response = $this->sendRequestShipX(self::URL_SHIPX_RATE, $data, $storeSetting->value);
            if ($response->status() != 200) {
                throw new \Exception('ShipX error: ' . $response->body() . ' - ' . $response->status() . 'for shipment id: ' . $shipment->id . 'order number: ' . $dataSaleOrder->order_number);
            }
            $dataRes = $response->json();
            if (isset($dataRes['error'])) {
                throw new \Exception('ShipX error: ' . $dataRes['error'] . ' - ' . $response->status() . 'for shipment id: ' . $shipment->id . 'order number: ' . $dataSaleOrder->order_number);
            }

            $dataResRate = $dataRes['rates'];
            if (count($dataResRate) == 0) {
                throw new \Exception('ShipX error: No rate found from shipx for shipment id: ' . $shipment->id . 'order number: ' . $dataSaleOrder->order_number);
            }
            $rate = 0;
            foreach ($dataResRate as $item) {
                if ($item['service'] == $shipment->service_code) {
                    $rate = $item['rate'];
                    break;
                }
            }
            if ($rate == 0) {
                throw new \Exception('ShipX error: not match service code with shipx rate ' . $shipment->service_code . 'for shipment id: ' . $shipment->id . 'order number: ' . $dataSaleOrder->order_number);
            }
            $shipment->update([
                'shipx_rate' => $rate,
            ]);
        } catch (\Exception $e) {
            $googleChat = Setting::where('name', Setting::GOOGLE_SPACE_SHIPX_RATE_ERROR)->first();
            sendGoogleChat($googleChat->value ?? '', $e->getMessage());
            // bắn ra google chat để biết được thông tin lỗi tránh ảnh hưởng cho invoice
        }
    }

    protected function sendRequestShipX($url, $data, $token)
    {
        $response = Http::timeout(30)->withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ])->post(trim($url), $data);

        return $response;
    }
}
