<?php

namespace App\Jobs;

use App\Exceptions\ShipXException;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\StoreSetting;
use App\Repositories\LabelRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class GetRateShipXJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shipmentId;

    protected $lastError;

    const URL_SHIPX_RATE = 'https://shipx-api.swiftpod.com/v1/shipments';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shipmentId)
    {
        $this->shipmentId = $shipmentId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $shipment = Shipment::where('id', $this->shipmentId)->first();
            if (!$shipment) {
                throw ShipXException::shipmentNotFound($this->shipmentId);
            }
            $storeSetting = StoreSetting::where('store_id', $shipment->store_id)
                            ->where('key', StoreSetting::KEY_SHIPX_API_KEY)
                            ->first();
            if (!$storeSetting) {
                throw ShipXException::storeSettingNotFound($shipment->id);
            }
            $dataSaleOrder = SaleOrder::with(
                'store',
                'warehouse',
                'addressSaleOrder',
                'items.product',
            )
                ->with(['storeAddress' => function ($q) {
                    $q->where('type_address', '=', 'return_address');
                }])
            ->where('id', $shipment->order_id)
            ->first();
            if (!$dataSaleOrder) {
                throw ShipXException::saleOrderNotFound($shipment->id);
            }
            $labelRepository = new LabelRepository();
            $addressTo = $labelRepository->dataAddressTo($dataSaleOrder->addressSaleOrder, false, $dataSaleOrder);
            if (strtoupper($addressTo['country']) !== 'US') {
                $customsInfo = $labelRepository->dataCustomsInfoV2($dataSaleOrder);
            } else {
                $customsInfo = in_array($addressTo['city'], SaleOrderAddress::IN_US_NEED_CUSTOMS) || in_array($addressTo['state'], SaleOrderAddress::IN_US_NEED_CUSTOMS_FOR_STATE) ? $this->dataCustomsInfoV2($dataSaleOrder) : '';
            }
            $addressFrom = $labelRepository->dataAddressFrom($dataSaleOrder->warehouse, $dataSaleOrder->store?->name, 'USPS');
            $addressReturn = $labelRepository->dataAddressReturn($dataSaleOrder->storeAddress, $dataSaleOrder->merchant_name, $dataSaleOrder->addressSaleOrder);
            $dataParcel = [
                'length' => $shipment->dimension_length ?? 9,
                'width' => $shipment->dimension_width ?? 6,
                'height' => $shipment->dimension_height ?? 2,
                'weight' => $shipment->weight_value ?? 0,
            ];
            $data = [
                'from_address' => $addressFrom,
                'to_address' => $addressTo,
                'return_address' => $addressReturn,
                'parcel' => $dataParcel,
                'customs_info' => $customsInfo,
            ];
            if (!empty($dataSaleOrder->ioss_number)) {
                $data['tax_identifiers'] = [
                    [
                        'entity' => 'SENDER',
                        'tax_id' => $dataSaleOrder->ioss_number,
                        'tax_id_type' => $dataSaleOrder->tax_id_type,
                        'issuing_country' => $addressTo['country'],
                    ]
                ];
            }
            $response = $this->sendRequestShipX(self::URL_SHIPX_RATE, $data, $storeSetting->value);
            if ($response->status() != 200) {
                throw ShipXException::apiError(
                    $response->body(),
                    $response->status(),
                    $shipment->id,
                    $dataSaleOrder->order_number
                );
            }
            $dataRes = $response->json();
            if (isset($dataRes['error'])) {
                throw ShipXException::apiError(
                    $dataRes['error'],
                    $response->status(),
                    $shipment->id,
                    $dataSaleOrder->order_number
                );
            }

            $dataResRate = $dataRes['rates'];
            if (count($dataResRate) == 0) {
                throw ShipXException::noRatesFound($shipment->id, $dataSaleOrder->order_number);
            }
            $rate = 0;
            foreach ($dataResRate as $item) {
                if ($item['service'] == $shipment->service_code) {
                    $rate = $item['rate'];
                    break;
                }
            }
            if ($rate == 0) {
                throw ShipXException::serviceCodeMismatch(
                    $shipment->service_code,
                    $shipment->id,
                    $dataSaleOrder->order_number
                );
            }
            $shipment->update([
                'shipx_rate' => $rate,
            ]);
        } catch (\Exception $e) {
            $this->handleJobException($e);
        }
    }

    protected function sendRequestShipX($url, $data, $token)
    {
        $response = Http::timeout(30)->withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
        ])->post(trim($url), $data);

        return $response;
    }

    /**
     * Handle job exceptions with detailed logging and notifications
     */
    protected function handleJobException(\Exception $e)
    {
        $errorData = $this->buildErrorData($e);

        // Store error for testing/debugging
        $this->storeError($e);

        // Log detailed error information
        \Log::error('GetRateShipXJob failed', $errorData);

        // Send notification to Google Chat
        $this->sendErrorNotification($e->getMessage(), $errorData);

        // Optionally re-throw for specific error types that should fail the job
        if ($this->shouldFailJob($e)) {
            throw $e;
        }
    }

    /**
     * Build comprehensive error data for logging
     */
    protected function buildErrorData(\Exception $e): array
    {
        return [
            'shipment_id' => $this->shipmentId,
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'stack_trace' => $e->getTraceAsString(),
            'timestamp' => now()->toISOString(),
            'job_class' => self::class,
        ];
    }

    /**
     * Send error notification to Google Chat
     */
    protected function sendErrorNotification(string $message, array $errorData = [])
    {
        try {
            $googleChat = Setting::where('name', Setting::GOOGLE_SPACE_SHIPX_RATE_ERROR)->first();

            if ($googleChat && $googleChat->value) {
                $formattedMessage = $this->formatErrorMessage($message, $errorData);
                sendGoogleChat($googleChat->value, $formattedMessage);
            }
        } catch (\Exception $e) {
            // Don't let notification errors break the job
            \Log::error('Failed to send Google Chat notification', [
                'original_error' => $message,
                'notification_error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Format error message for Google Chat
     */
    protected function formatErrorMessage(string $message, array $errorData): string
    {
        return sprintf(
            "🚨 GetRateShipXJob Error\n" .
            "Shipment ID: %s\n" .
            "Error: %s\n" .
            "Time: %s",
            $errorData['shipment_id'] ?? 'Unknown',
            $message,
            $errorData['timestamp'] ?? now()->toISOString()
        );
    }

    /**
     * Determine if job should fail based on exception type
     */
    protected function shouldFailJob(\Exception $e): bool
    {
        // Define critical errors that should fail the job
        $criticalErrors = [
            \Illuminate\Database\QueryException::class,
            \Illuminate\Http\Client\ConnectionException::class,
        ];

        // Check if it's a critical error
        foreach ($criticalErrors as $criticalError) {
            if ($e instanceof $criticalError) {
                return true;
            }
        }

        // For ShipXException, decide based on error type
        if ($e instanceof ShipXException) {
            $criticalShipXErrors = ['api_error'];
            return in_array($e->getErrorType(), $criticalShipXErrors);
        }

        // Don't fail for business logic errors (shipment not found, etc.)
        return false;
    }

    /**
     * Get the last error that occurred in this job
     * Useful for testing
     */
    public function getLastError(): ?array
    {
        return $this->lastError ?? null;
    }

    /**
     * Store error for testing purposes
     */
    protected function storeError(\Exception $e)
    {
        $this->lastError = $this->buildErrorData($e);
    }
}
