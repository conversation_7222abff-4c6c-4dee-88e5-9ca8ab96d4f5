<?php

namespace App\Http\Service;

use App\Models\BarcodePrinted;
use App\Models\IntegrateCallbackLog;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductSize;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Models\StoreIntegrate;
use App\Repositories\PrintingRepository;
use App\Repositories\SaleOrderRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Imagick;
use Intervention\Image\Facades\Image;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class ConvertService
{
    protected $imagick;

    const MAX_LENGTH_PAGE = 30;

    const MAX_ROW_PAGE = 5;

    const MAX_COLUMN_PAGE = 6;

    const RATIO_IN_TO_PIXEL = 96;

    const RATIO_IN_TO_MM = 25.4;

    const RATIO_PIXEL_TO_MM = 0.2645833333;

    const MAX_SIZE_IMAGE = 10000000;

    const PAGE_WIDTH = 1587;

    const PAGE_WIDTH_PX = 18744;

    const PAGE_WIDTH_PT = 4500;

    const PAGE_HEIGHT = 762;

    const PAGE_HEIGHT_PX = 9000;

    const PAGE_HEIGHT_PT = 2160;

    const DPI = 300;

    const RATIO_IN_TO_POINTS = 72;

    const BACKGROUND_COLOR_BLACK = '#050403';

    const TUMBLER_STYLE = 'TMBS';

    public function convertMugsToPdf($pdf_convert)
    {
        try {
            $alertService = new AlertService();
            $orderItemBarcodes = SaleOrderItemBarcode::getItemsToConvertPdf($pdf_convert->id);
            echo "barcodePrintedId $pdf_convert->id : first total item == {$orderItemBarcodes->count()}\n";

            if ($orderItemBarcodes->isEmpty()) {
                $message = "barcodePrintedId $pdf_convert->id : bug error item == 0, stop\n";
                echo $message;
                $alertService->alertForConvertMugError($message);
                $this->updateConvertFail($pdf_convert);

                return false;
            }

            $product = Product::findByIdAndPresetSku($pdf_convert->product_id);

            if (!$product) {
                $message = "barcodePrintedId $pdf_convert->id : bug error preset not found, stop\n";
                echo $message;
                $alertService->alertForConvertMugError($message);
                $this->updateConvertFail($pdf_convert);

                return false;
            }

            $name = $product->style . '-' . $product->size . 'Z-' . $product->color;
            $outputs = [];
            $idConvertSuccess = [];
            $idConvertFail = [];
            $convert_percent = 0;
            $mugs_path = storage_path('app/public/');
            $totalRow = ceil(count($orderItemBarcodes) / self::MAX_COLUMN_PAGE);
            $newOrderIds = [];
            $pxToPt = self::DPI / self::RATIO_IN_TO_POINTS;
            $dataImportHistory = [];
            ///Make Mug layer cut
            $positionIllus = [];
            $positionIllus['id'] = $pdf_convert->id;
            $positionIllus['is_convert_mug'] = true;
            $position = [];
            $dieCutHeightDefault = 0;
            $dieCutParentWidth = 0;

            foreach ($orderItemBarcodes as $key => $item) {
                if ($item->saleOrder->order_status == SaleOrder::NEW_ORDER) {
                    $newOrderIds[] = $item->order_id;
                }

                echo "\nsale order : {$item->saleOrder->id}" . PHP_EOL;
                $printSides = trim($item->orderItem->print_sides);

                if (strlen($printSides) > 1) {
                    $message = "barcodePrintedId $pdf_convert->id : $item->label_id - bug error print_sides > 1 0, continue\n";
                    echo $message;
                    $idConvertFail[] = $item->id;
                    $alertService->alertForConvertMugError($message);

                    continue;
                }

                $productPrintSide = ProductPrintSide::where('code_wip', $printSides)->first();

                if (!$productPrintSide) {
                    $message = "barcodePrintedId $pdf_convert->id : $item->label_id - bug error product_print_side $printSides not found, continue\n";
                    echo $message;
                    $idConvertFail[] = $item->id;
                    $alertService->alertForConvertMugError($message);

                    continue;
                }

                $codeName = $productPrintSide->code_name;
                $codeWip = $productPrintSide->code_wip;
                $presetName = $codeName . '_size';

                if (!$product->printingPresetSku->{$presetName}) {
                    $message = "barcodePrintedId $pdf_convert->id : $item->label_id - bug error sku : $product->sku missing preset $presetName, continue\n";
                    echo $message;
                    $idConvertFail[] = $item->id;
                    $alertService->alertForConvertMugError($message);

                    if (!isset($dataImportHistory[$item->order_id])) {
                        $dataImportHistory[$item->order_id] = [
                            'user_id' => null,
                            'employee_id' => null,
                            'order_id' => $item->order_id,
                            'type' => SaleOrderHistory::UPDATE_ORDER_CONVERT_MUGS_TYPE,
                            'message' => 'Printing mug failed due to a missing preset.',
                            'created_at' => Carbon::now()->toDateTimeString()
                        ];
                    }

                    continue;
                }

                $background_color = $product->printingPresetSku->background_color;
                $background_color = is_null($background_color) ? 'white' : $background_color;
                $printingPresetConfig = $this->getPresetPrintArea($background_color, $codeWip, $product->size);

                if (empty($printingPresetConfig)) {
                    $message = "barcodePrintedId $pdf_convert->id : bug error preset not found, stop\n";
                    echo $message;
                    $idConvertFail[] = $item->id;
                    $alertService->alertForConvertMugError($message);

                    continue;
                }

                $dieCutAreaInch = $printingPresetConfig['dieCutAreaInch'];
                $maxPrintAreaInch = $printingPresetConfig['maxPrintAreaInch'];
                $safeAreaInch = $printingPresetConfig['safeAreaInch'];
                $margin = $printingPresetConfig['margin'];
                $bottom = $printingPresetConfig['bottom'];
                $dieCutArea = explode('x', $dieCutAreaInch);
                $maxPrintArea = explode('x', $maxPrintAreaInch);
                $safeArea = explode('x', $safeAreaInch);
                $dieCutHeightDefault = (float) $dieCutArea[1] + 1;

                $area = [
                    'width_image' => ((float) $safeArea[0]) * self::DPI,
                    'height_image' => ((float) $safeArea[1]) * self::DPI
                ];

                $totalItems = count($orderItemBarcodes); // Tổng số lượng item
                $width = (float) $dieCutArea[0] * self::DPI;
                $dieCutParentWidth = $totalItems <= self::MAX_COLUMN_PAGE
                    ? ($totalItems * $width) + (($totalItems - 1) * $margin * 300)
                    : (self::MAX_COLUMN_PAGE * $width) + ((self::MAX_COLUMN_PAGE - 1) * $margin * 300);
                $left = ((float) $dieCutArea[0]) * self::DPI * ($key % self::MAX_COLUMN_PAGE) + $margin * 300 * ($key % self::MAX_COLUMN_PAGE) + 100;
                echo "left of key $key: $left \n";
                $row = ceil(($key + 1) / self::MAX_COLUMN_PAGE);
                $topIllustrator = $top = ($row - 1) * (((float) $dieCutArea[1]) * self::DPI + (self::DPI * 0.71)) + 0.71 * self::DPI;
                $ratio = $area['width_image'] / $area['height_image'];
                $totalRow = ceil(count($orderItemBarcodes) / self::MAX_COLUMN_PAGE);
                $qr_label_id = QrCode::size(100)->generate($item->label_id);
                $hardGoodAndShirt = resolve(SaleOrderRepository::class)->getHardGoodAndShirt($item->order_id);
                $isHardGoodAndShirt = count($hardGoodAndShirt) >= 2;
                $shirtQuantity = $isHardGoodAndShirt ? $hardGoodAndShirt[0] : null;

                // order item has two image files or just has image one side
                if ($item->orderItem->images->first()->image_height * $ratio > $item->orderItem->images->first()->image_width * 2) {
                    echo 'item has two images order_item_id: ' . $item->orderItem->id . "\n";
                    $image_urls = [];
                    $transparent = null;

                    foreach ($item->orderItem->images as $image) {
                        // download image
                        $file = 'mugs/' . $pdf_convert->id . '/' . $image->id . '.png';

                        if ($image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$image->order_date}/{$image->sku}-{$image->print_side}.png")) {
                            $url = file_get_contents(env('AWS_URL') . '/artwork/' . $image->order_date . '/' . $image->sku . '-' . $image->print_side . '.png');
                        } else {
                            $url = file_get_contents($this->addHttp($image->image_url));
                        }

                        if (!$url || is_null($url)) {
                            $idConvertFail[] = $item->id;
                            $message = "barcodePrintedId $pdf_convert->id : $item->label_id - url null or hash_md5 invalid case 1, continue\n";
                            $alertService->alertForConvertMugError($message);

                            continue;
                        }

                        Storage::disk('public')->put($file, $url);

                        if (!Storage::disk('public')->exists($file)) {
                            echo "bug download failed \n";
                            $message = "barcodePrintedId $pdf_convert->id : $item->label_id - bug download failed case 1, continue\n";
                            $alertService->alertForConvertMugError($message);
                            $idConvertFail[] = $item->id;

                            continue;
                        }

                        // optimize image
                        $this->optimizeImage($mugs_path . $file);
                        $image_urls[] = $mugs_path . $file;

                        if (is_null($transparent)) {
                            // make transparent image
                            $transparent = new Imagick();
                            $transparent->newImage($image->image_height * $ratio - $image->image_width * 2, $image->image_height, 'transparent');
                            $transparent->setImageOpacity(0.0);
                        }
                    }

                    if (empty($image_urls)) {
                        $idConvertFail[] = $item->id;
                        $message = "barcodePrintedId $pdf_convert->id : image_urls is empty, continue\n";
                        $alertService->alertForConvertMugError($message);

                        continue;
                    }

                    // append images
                    $fullimage = new Imagick();
                    $fullimage->addImage(new Imagick($image_urls[0]));
                    $fullimage->borderImage($background_color, 1, 1);
                    $fullimage->addImage($transparent);
                    $fullimage->borderImage($background_color, 1, 1);
                    $fullimage->addImage(new Imagick(count($image_urls) > 1 ? $image_urls[1] : $image_urls[0]));
                    $fullimage->borderImage($background_color, 1, 1);
                    $fullimage->resetIterator();
                    $appendImage = $fullimage->appendImages(false);
                    $appendImage->setImageFormat('png');
                    $appendImage->resizeImage($area['width_image'], $area['height_image'], Imagick::FILTER_UNDEFINED, 1);
                    $imageSize = $this->checkImageSize($safeArea, $item);

                    $outputs[] = [
                        'isHardGoodAndShirt' => $isHardGoodAndShirt,
                        'shirtQuantity' => $shirtQuantity,
                        'label' => $item->label_id,
                        'qrCode' => '<img src="data:image/svg+xml;base64,' . base64_encode($qr_label_id) . '"  width="150" height="150" />',
                        'image' => 'data:image/png;base64, ' . base64_encode($appendImage->getImageBlob()),
                        'topPs' => $top,
                        'leftPs' => $left,
                        'itemNumber' => $item->barcode_number . '/' . $item->order_quantity,
                        'width_area_die_cut' => ((float) $dieCutArea[0]) * self::DPI,
                        'height_area_die_cut' => ((float) $dieCutArea[1]) * self::DPI,
                        'width_area_max_print' => ((float) $maxPrintArea[0]) * self::DPI,
                        'height_area_max_print' => ((float) $maxPrintArea[1]) * self::DPI,
                        'width_area_safe' => ((float) $safeArea[0]) * self::DPI,
                        'height_area_safe' => ((float) $safeArea[1]) * self::DPI,
                        'background_color' => $background_color,
                        'width' => $imageSize['width'],
                        'height' => $imageSize['height'],
                        'bottom' => $bottom
                    ];

                    echo 'case 1';
                    $position[] = [
                        'top' => ($topIllustrator / $pxToPt),
                        'left' => ($left) / $pxToPt,
                        'width_die_cut' => $dieCutArea[0] * 72,
                        'height_die_cut' => $dieCutArea[1] * 72,
                    ];
                } else {
                    // download image
                    $file = 'mugs/' . $pdf_convert->id . '/' . $item->orderItem->images->first()->id . '.png';
                    $isOptimized = false;
                    $img = $item->orderItem->images->first();

                    if ($img->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$img->order_date}/{$img->sku}-{$img->print_side}.png")) {
                        $imageUrl = env('AWS_URL') . '/artwork/' . $img->order_date . '/' . $img->sku . '-' . $img->print_side . '.png';
                    } else {
                        $imageUrl = $this->addHttp($img->image_url);
                    }

                    if (!$imageUrl || is_null(file_get_contents($imageUrl))) {
                        $idConvertFail[] = $item->id;
                        $message = "barcodePrintedId $pdf_convert->id : $item->label_id url null or hash_md5 invalid case 2, continue\n";
                        $alertService->alertForConvertMugError($message);

                        continue;
                    }

                    // if file size too large
                    if ($img->image_size > self::MAX_SIZE_IMAGE) {
                        echo 'optimize image_id: ' . $img->id . "\n";
                        Storage::disk('public')->put($file, file_get_contents($imageUrl));

                        if (!Storage::disk('public')->exists($file)) {
                            echo "bug download failed \n";
                            $idConvertFail[] = $item->id;
                            $message = "barcodePrintedId $pdf_convert->id : $item->label_id - bug download failed case 2, continue\n";
                            $alertService->alertForConvertMugError($message);

                            continue;
                        }

                        // optimize image
                        $this->optimizeImage($mugs_path . $file);
                        $isOptimized = true;
                    }

                    $imageSize = $this->checkImageSize($safeArea, $item);
                    $optimizedPath = $mugs_path . 'mugs/' . $pdf_convert->id . '/' . $img->id . '.png';
                    if ($isOptimized && file_exists($optimizedPath)) {
                        $imageUrl = $optimizedPath;
                    }
                    $imageOptimize = file_get_contents($imageUrl);
                    $imageWidth = $imageSize['width'];
                    $imageHeight = $imageSize['height'];
                    $outputs[] = [
                        'isHardGoodAndShirt' => $isHardGoodAndShirt,
                        'shirtQuantity' => $shirtQuantity,
                        'label' => $item->label_id,
                        'qrCode' => '<img src="data:image/svg+xml;base64,' . base64_encode($qr_label_id) . '"  width="150" height="150" />',
                        'image' => 'data:image/png;base64, ' . base64_encode($imageOptimize),
                        'topPs' => $top,
                        'leftPs' => $left,
                        'itemNumber' => $item->barcode_number . '/' . $item->order_quantity,
                        'width_area_die_cut' => ((float) $dieCutArea[0]) * self::DPI,
                        'height_area_die_cut' => ((float) $dieCutArea[1]) * self::DPI,
                        'width_area_max_print' => ((float) $maxPrintArea[0]) * self::DPI,
                        'height_area_max_print' => ((float) $maxPrintArea[1]) * self::DPI,
                        'width_area_safe' => ((float) $safeArea[0]) * self::DPI,
                        'height_area_safe' => ((float) $safeArea[1]) * self::DPI,
                        'background_color' => $background_color,
                        'width' => $imageWidth,
                        'height' => $imageHeight,
                        'bottom' => $bottom
                    ];
                    echo 'case 2';
                    $position[] = [
                        'top' => ($topIllustrator / $pxToPt),
                        'left' => ($left) / $pxToPt,
                        'width_die_cut' => $dieCutArea[0] * 72,
                        'height_die_cut' => $dieCutArea[1] * 72,
                    ];
                    unset($imageOptimize);
                }

                $convert_percent++;
                $idConvertSuccess[] = $item->id;
            }

            $log_path = storage_path('logs/ormt/' . $pdf_convert->id . '/');

            if (!is_dir($log_path)) {
                mkdir($log_path, 0777, true);
            }

            $positionIllus['positions'] = $position;
            $pdfFilePath = $mugs_path . '/mug.pdf';  // Original PDF generated by Pdf::loadView
            $finalPdfPath = $mugs_path . '/layer.pdf';  // Final PDF after conversion
            $options = [
                'data' => $outputs,
                'name' => $name,
                'dieCutParentWidth' => $dieCutParentWidth,
            ];
            $paperOptions = [0, 0, self::PAGE_WIDTH_PT, $dieCutHeightDefault * $totalRow * self::RATIO_IN_TO_POINTS + 85];

            if (!empty(env('APP_DEBUG'))) {
                $uuid = Str::uuid();
                Log::info("ConvertService.convertMugsToPdf print_mug_layer_cut data options uuid.$uuid", $options);
                Log::info("ConvertService.convertMugsToPdf print_mug_layer_cut paper options uuid.$uuid", $paperOptions);
            }

            $pdf = Pdf::loadView('print_mug_layer_cut', $options)->setOptions([
                'dpi' => self::DPI,
                'logOutputFile' => storage_path('logs/pdf.log'),
                'tempDir' => $log_path
            ])
                ->setPaper($paperOptions)
                ->save($pdfFilePath);  // Save the generated PDF to a file
            exec("convert -density 300 $pdfFilePath -quality 100 $finalPdfPath");

            $finalPdfContent = file_get_contents($finalPdfPath);

            Storage::disk('s3')->put('/mugs/' . $pdf_convert->id . '.pdf', $finalPdfContent);
            if (is_dir($mugs_path . 'mugs/' . $pdf_convert->id)) {
                File::deleteDirectory($mugs_path . 'mugs/' . $pdf_convert->id);
            }
            $pdf = null;

            echo "last total item == {$orderItemBarcodes->count()}\n";

            $positionIllus['width'] = self::PAGE_WIDTH_PT;
            $positionIllus['height'] = $dieCutHeightDefault * $totalRow * self::RATIO_IN_TO_POINTS + 85;

            DB::beginTransaction();
            $pdf_convert->convert_percent = $convert_percent;
            $pdf_convert->convert_status = BarcodePrinted::ACTIVE;
            $pdf_convert->converted_at = Carbon::now();
            $pdf_convert->quantity = $orderItemBarcodes->count();
            $pdf_convert->positions = !empty($positionIllus) ? json_encode($positionIllus) : null;
            $pdf_convert->save();

            SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => $pdf_convert->id,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_SUCCESS,
                        'employee_pull_id' => $pdf_convert->employee_id,
                        'print_barcode_at' => Carbon::now(),
                        // add logic for auto shipping
                        'employee_print_id' => $pdf_convert->employee_id,
                        'printed_at' => Carbon::now(),
                        //   'pulled_at' => null // Carbon::now()
                    ],
                );
            SaleOrderItemBarcode::whereIn('id', $idConvertFail)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_FAIL,
                        'retry_convert' => DB::raw('retry_convert+1')
                    ],
                );

            // Update barcode printed status
            $saleOrderIds = SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)->pluck('order_id')->toArray();
            SaleOrder::whereIn('id', array_unique($saleOrderIds))
                ->where('order_status', SaleOrder::NEW_ORDER)
                ->update(
                    [
                        'order_status' => SaleOrder::IN_PRODUCTION,
                        'order_production_at' => Carbon::now()
                    ],
                );

            if (!empty($newOrderIds)) {
                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, array_unique($newOrderIds));
            }

            if (!empty($dataImportHistory)) {
                // Lấy các giá trị từ mảng $dataImportHistory
                SaleOrderHistory::insert(array_values($dataImportHistory));
            }

            // xu ly logic in coc bao sang ben gelato
            $skipOrderId = SaleOrder::getIDBeforeDays(10);
            $saleOrderGelatos = SaleOrder::select('id', 'external_number')
                ->whereIn('id', array_unique($saleOrderIds))
                ->where('id', '>=', $skipOrderId)
                ->where('store_id', Store::STORE_GELATO)
                ->get();
            if ($saleOrderGelatos->isNotEmpty()) {
                $integrateCallbackLogOrders = IntegrateCallbackLog::whereIn('order_id', array_unique($saleOrderIds))
                    ->where('event', IntegrateCallbackLog::ORDER_NOTIFY_EVENT)
                    ->where('data', 'like', '%"status":"printed"%')
                    ->where('status', true)
                    ->pluck('external_number', 'order_id')->toArray();
                foreach ($saleOrderGelatos as $saleOrderGelato) {
                    if (!isset($integrateCallbackLogOrders[$saleOrderGelato->id])) {
                        $dataPostBack = [
                            'order_id' => $saleOrderGelato->id,
                            'event' => IntegrateCallbackLog::ORDER_NOTIFY_EVENT,
                            'data_send_gelato' => [
                                'timestamp' => \Carbon\Carbon::now()->toIso8601String(),
                                'orderId' => $saleOrderGelato->external_number,
                                'status' => 'printed',
                                'message' => 'Order has been printed',
                            ]
                        ];
                        handleJob(StoreIntegrate::JOB_POST_BACK_GELATO, $dataPostBack);
                    }
                }
            }

            // update print_at cho order
            if (!empty($saleOrderIds)) {
                $printingRepository = new PrintingRepository();
                foreach ($saleOrderIds as $ids) {
                    $printingRepository->updateOrderPrinted($ids);
                }
            }

            echo 'done ==> pdf_converted_id = ' . $pdf_convert->id . "\n";
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertFail($pdf_convert);
            $message = "barcodePrintedId $pdf_convert->id : error ==> {$e->getMessage()}, {$e->getLine()}\n";
            $alertService = new AlertService();
            $alertService->alertForConvertMugError($message);
        }
    }

    protected function getPresetPrintArea($background_color, $area, $size)
    {
        $details = [];
        switch ($background_color) {
            case self::BACKGROUND_COLOR_BLACK:
                switch ($size) {
                    case ProductSize::SIZE_11OZ_NAME:
                        $details['dieCutAreaInch'] = '9.7x4.8';
                        $details['maxPrintAreaInch'] = '9.7x3.75';
                        $details['safeAreaInch'] = '7.2533x3.2';
                        $details['margin'] = 0.5;
                        $details['bottom'] = 10;
                        break;
                    case ProductSize::SIZE_15OZ_NAME:
                        $details['dieCutAreaInch'] = '10x5.3';
                        $details['maxPrintAreaInch'] = '10x4.2';
                        $details['safeAreaInch'] = '7.64x3.6';
                        $details['margin'] = 0.2;
                        $details['bottom'] = 10;
                        break;
                }
                break;

            default:
                switch ($area) {
                    case ProductPrintSide::CODE_WIP_FRONT:
                        switch ($size) {
                            case ProductSize::SIZE_11OZ_NAME:
                                $details['dieCutAreaInch'] = '9.6x4.9';
                                $details['maxPrintAreaInch'] = '8x3.8';
                                $details['safeAreaInch'] = '8x3.2';
                                $details['margin'] = 0.5;
                                $details['bottom'] = 0;
                                break;
                            case ProductSize::SIZE_15OZ_NAME:
                                $details['dieCutAreaInch'] = '10x5.25';
                                $details['maxPrintAreaInch'] = '8.25x4.25';
                                $details['safeAreaInch'] = '8.25x3.6';
                                $details['margin'] = 0.2;
                                $details['bottom'] = 0;
                                break;
                            case ProductSize::SIZE_20OZ_NAME:
                                $details['dieCutAreaInch'] = '9.33x9.03';
                                $details['maxPrintAreaInch'] = '9.33x8.11';
                                $details['safeAreaInch'] = '9.33x8.11';
                                $details['margin'] = 1;
                                $details['bottom'] = 0;
                                break;
                        }
                        break;

                    case ProductPrintSide::CODE_WIP_BLEED_MUG:
                        switch ($size) {
                            case ProductSize::SIZE_11OZ_NAME:
                                $details['dieCutAreaInch'] = '9.6x4.9';
                                $details['maxPrintAreaInch'] = '8.25x3.8';
                                $details['safeAreaInch'] = '8.25x3.8';
                                $details['margin'] = 0.5;
                                $details['bottom'] = 0;
                                break;
                            case ProductSize::SIZE_15OZ_NAME:
                                $details['dieCutAreaInch'] = '10x5.25';
                                $details['maxPrintAreaInch'] = '8.25x4.3';
                                $details['safeAreaInch'] = '8.25x4.3';
                                $details['margin'] = 0.2;
                                $details['bottom'] = 0;
                                break;
                        }
                        break;

                    case ProductPrintSide::CODE_WIP_MAX_PRINT:
                        switch ($size) {
                            case ProductSize::SIZE_11OZ_NAME:
                                $details['dieCutAreaInch'] = '9.6x4.9';
                                $details['maxPrintAreaInch'] = '9.14x3.8';
                                $details['safeAreaInch'] = '9.14x3.8';
                                $details['margin'] = 0.5;
                                $details['bottom'] = 0;
                                break;
                            case ProductSize::SIZE_15OZ_NAME:
                                $details['dieCutAreaInch'] = '10x5.25';
                                $details['maxPrintAreaInch'] = '9.5x4.3';
                                $details['safeAreaInch'] = '9.5x4.3';
                                $details['margin'] = 0.2;
                                $details['bottom'] = 0;
                                break;
                        }
                        break;
                }
                break;
        }

        return $details;
    }

    protected function optimizeImage($path)
    {
        if (filesize($path) > self::MAX_SIZE_IMAGE) {
            $optimize = new Imagick($path);
            $optimize->writeImage($path);
        }
    }

    protected function updateConvertFail($pdf_convert)
    {
        SaleOrderItemBarcode::where('barcode_printed_id', $pdf_convert->id)->update([
            'barcode_printed_id' => BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT,
            'retry_convert' => DB::raw('retry_convert+1')
        ]);

        $pdf_convert->convert_status = BarcodePrinted::FAILED;
        $pdf_convert->save();
    }

    protected function checkImageSize($front_size, $item)
    {
        $dpi = is_null($item->orderItem->images->first()->image_dpi) ? self::DPI : (int) explode('x', $item->orderItem->images->first()->image_dpi)[0];
        $image_width = ((float) $front_size[0]) * self::DPI;
        $image_height = ((float) $front_size[1]) * self::DPI;
        $origin_width = $item->orderItem->images->first()->image_width / $dpi * self::DPI;
        $origin_height = $item->orderItem->images->first()->image_height / $dpi * self::DPI;

        if ($origin_width < $image_width) {
            $image_width = $origin_width;
        }

        if ($origin_height < $image_height) {
            $image_height = $origin_height;
        }

        return [
            'width' => $image_width,
            'height' => $image_height
        ];
    }

    public function convertOrmtToPdfByDompdf($pdf_convert)
    {
        ini_set('memory_limit', '5G');

        $product = Product::findByIdAndPresetSkuUv($pdf_convert->product_id);

        if (!$product) {
            echo "bug error product not found\n";
            $this->updateConvertOrmtFail($pdf_convert);

            return false;
        }

        $page_width = $product->uvPresetSku->page_width * self::DPI;
        $page_height = $product->uvPresetSku->page_height * self::DPI;
        $pdf_width_pt = $product->uvPresetSku->page_width * self::RATIO_IN_TO_POINTS;
        $pdf_height_pt = $product->uvPresetSku->page_height * self::RATIO_IN_TO_POINTS;
        $total_item_on_row = $product->uvPresetSku->max_item_on_row;
        $max_row = $product->uvPresetSku->max_row;
        $margin_right = $product->uvPresetSku->margin_x;
        $margin_bottom = $product->uvPresetSku->margin_y;
        $is_circle = $product->uvPresetSku->is_circle;
        $is_mirror = $product->uvPresetSku->is_mirror;
        $border = $product->uvPresetSku->is_border;

        $this->convertOrmt($pdf_convert, $product, $page_width, $page_height, $pdf_width_pt, $pdf_height_pt, $total_item_on_row, $max_row, $margin_right, $margin_bottom, $is_circle, $is_mirror, $border);
    }

    protected function convertOrmt($pdf_convert, $product, $page_width, $page_height, $pdf_width_pt, $pdf_height_pt, $total_item_on_row, $max_row, $margin_right, $margin_bottom, $is_circle, $is_mirror, $border)
    {
        echo "convert ornament product id : $pdf_convert->product_id\n";

        $max_size_image = 7300000;
        $outputs = [];
        $convertSuccess = [];
        $convertFail = [];
        $convert_percent = 0;
        $ormt_path = storage_path('app/public/ormt/');
        $log_path = storage_path('logs/ormt/' . $pdf_convert->id . '/');
        $total_row = ceil(count($pdf_convert->options) / $total_item_on_row);
        $page_height_by_row = $page_height / $max_row * $total_row;
        $quantity = count($pdf_convert->options);

        $platen_size = explode('x', $product->printingPresetSku->platen_size);
        $platenWidth = (float) $platen_size[0] * self::DPI + 1.5;
        $platenHeight = (float) $platen_size[1] * self::DPI + 1.5;

        $imageSize = explode('x', $product->printingPresetSku->front_size);
        $imageWidth = (float) $imageSize[0] * self::DPI + 1.5;
        $imageHeight = (float) $imageSize[1] * self::DPI + 1.5;

        try {
            if (!is_dir($ormt_path)) {
                mkdir($ormt_path, 0777, true);
            }

            echo 'Step 1: ' . memory_get_usage() . PHP_EOL;

            foreach ($pdf_convert->options as $key => $option) {
                $left = $page_width - $platenWidth * ($key % $total_item_on_row + 1) - $margin_right * ($key % $total_item_on_row);
                $row = ceil(($key + 1) / $total_item_on_row);
                $top = $page_height_by_row - $platenHeight * $row - (ceil(($key + 1) / $total_item_on_row) - 1) * $margin_bottom;

                if (is_null($option['label_id']) || is_null($option['image_id'] || is_null($option['side']))) {
                    $outputs[] = $this->handleBlankPosition($top, $left, $platenWidth, $platenHeight);

                    continue;
                }

                $barcode = SaleOrderItemBarcode::findByLabelId($option['label_id']);

                if (!$barcode) {
                    $outputs[] = $this->handleBlankPosition($top, $left, $platenWidth, $platenHeight);

                    continue;
                }

                $image = SaleOrderItemImage::with('imageHash')->where('id', $option['image_id'])->first();

                if (!$image) {
                    $outputs[] = $this->handleBlankPosition($top, $left, $platenWidth, $platenHeight);

                    continue;
                }

                if ($image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$image->order_date}/{$image->sku}-{$image->print_side}.png")) {
                    $file = file_get_contents(env('AWS_URL') . '/artwork/' . $image->order_date . '/' . $image->sku . '-' . $image->print_side . '.png');
                } else {
                    $file = file_get_contents($image->image_url);
                }

                if (!$file || is_null($file)) {
                    echo 'download fail image_id: ' . $image->id . "\n";
                    $outputs[] = [
                        'image' => '',
                        'top' => $top,
                        'left' => $left,
                        'width' => $platenWidth,
                        'height' => $platenHeight,
                        'image_width' => $imageWidth,
                        'image_height' => $imageHeight
                    ];
                    $convertFail[] = $barcode;

                    continue;
                }

                $optimize = new Imagick();
                $optimize->readImageBlob($file);
                $optimize->stripImage();
                $file = $optimize->getImageBlob();
                unset($optimize);
                $outputs[] = [
                    'image' => 'data:image/png;base64, ' . base64_encode($file),
                    'top' => $top,
                    'left' => $left,
                    'width' => $platenWidth,
                    'height' => $platenHeight,
                    'image_width' => $imageWidth,
                    'image_height' => $imageHeight
                ];
                $convertSuccess[] = $barcode;
                $convert_percent++;
                $file = null;
                echo "download $convert_percent\n";
            }

            echo 'Step 2: ' . memory_get_usage() . PHP_EOL;

            if (!is_dir($log_path)) {
                mkdir($log_path, 0777, true);
            }

            app()->make('view.engine.resolver')->register('blade', function () {
                return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
            });

            $pdf = Pdf::loadView('ornaments_dom_pdf', [
                'data' => $outputs,
                'border_radius' => $is_circle ? '50%' : '',
                'webkit' => $is_mirror ? 'scaleX(-1)' : '',
                'transform' => $is_mirror ? 'scaleX(-1)' : '',
                'border' => $border ? '2px solid black' : 'none',
                'padding_top' => ($platenHeight - $imageHeight) / 2
            ])->setOptions(['dpi' => self::DPI, 'logOutputFile' => storage_path('logs/pdf.log'), 'tempDir' => $log_path])->setPaper([0, 0, $pdf_width_pt, $pdf_height_pt / $max_row * $total_row]);

            $path_rgb = $ormt_path . $pdf_convert->id . '-rgb.pdf';
            echo "save $path_rgb\n";
            $pdf->save($path_rgb);
            echo "convert cymk $path_rgb\n";

            unset($pdf);
            unset($outputs);

            echo 'Step 3: ' . memory_get_usage() . PHP_EOL;

            if (file_exists($path_rgb)) {
                $path_cmyk = $ormt_path . $pdf_convert->id . '-cymk.pdf';
                $res = null;
                $status = null;
                $exec = 'gs -dSAFER -dBATCH -dNOPAUSE -dNOCACHE -sDEVICE=pdfwrite -dAutoRotatePages=/None -sColorConversionStrategy=CMYK -dProcessColorModel=/DeviceCMYK -dAutoFilterColorImages=false -dAutoFilterGrayImages=false -dColorImageFilter=/FlateEncode -dGrayImageFilter=/FlateEncode -dDownsampleMonoImages=false -dDownsampleGrayImages=false -sOutputFile=';
                exec($exec . $path_cmyk . ' ' . $path_rgb, $res, $status);
                echo "\n" . $exec . $path_cmyk . ' ' . $path_rgb . "\n";
                sleep(10);
                echo 'Step 4: ' . memory_get_usage() . PHP_EOL;
                $res = null;

                if ($status != 0) {
                    echo "convert rgb to cymk fail \n";
                    $this->updateConvertOrmtFail($pdf_convert);

                    if (File::exists($path_rgb)) {
                        File::delete($path_rgb);
                    }

                    if (File::exists($path_cmyk)) {
                        File::delete($path_cmyk);
                    }

                    if (is_dir($log_path)) {
                        File::deleteDirectory($log_path);
                    }

                    if (is_dir($ormt_path . '/' . $pdf_convert->id . '/')) {
                        File::deleteDirectory($ormt_path . '/' . $pdf_convert->id . '/');
                    }

                    return false;
                }

                echo 'status: ' . $status . "\n";
                $file_cmyk = file_get_contents($path_cmyk);
                Storage::disk('s3')->put('/ormt/' . $pdf_convert->id . '.pdf', $file_cmyk);
                echo 'Step 5: ' . memory_get_usage() . PHP_EOL;
                $file_cmyk = null;
                echo "upload to s3 done\n";
                File::delete($path_rgb);
                File::delete($path_cmyk);

                if (is_dir($log_path)) {
                    File::deleteDirectory($log_path);
                }

                if (is_dir($ormt_path . '/' . $pdf_convert->id . '/')) {
                    File::deleteDirectory($ormt_path . '/' . $pdf_convert->id . '/');
                }

                unset($exec, $pdf);
            }

            DB::beginTransaction();
            $pdf_convert->convert_percent = $convert_percent;
            $pdf_convert->convert_status = PdfConverted::ACTIVE;
            $pdf_convert->convert_at = Carbon::now();
            $pdf_convert->quantity = $quantity;
            $pdf_convert->save();

            if (!empty($convertSuccess)) {
                foreach ($convertSuccess as $barcode) {
                    $barcode->save();
                    PdfConvertedItem::where('barcode_id', $barcode->id)
                        ->where('pdf_converted_id', $pdf_convert->id)
                        ->update([
                            'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_SUCCESS
                        ]);
                }
            }

            if (!empty($convertFail)) {
                foreach ($convertFail as $barcode) {
                    PdfConvertedItem::where('barcode_id', $barcode->id)
                        ->where('pdf_converted_id', $pdf_convert->id)
                        ->update([
                            'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_FAIL
                        ]);
                }
            }

            echo 'done ==> pdf_converted_id = ' . $pdf_convert->id . "\n";
            DB::commit();
        } catch (Exception $e) {
            var_dump($e);
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertOrmtFail($pdf_convert);
        }
    }

    protected function handleBlankPosition($top, $left, $width, $height)
    {
        return [
            'image' => '',
            'top' => $top,
            'left' => $left,
            'width' => $width,
            'height' => $height
        ];
    }

    protected function updateConvertOrmtFail($pdf_convert)
    {
        PdfConvertedItem::where('pdf_converted_id', $pdf_convert->id)->update([
            'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_FAIL
        ]);

        $pdf_convert->convert_status = PdfConverted::FAIL;
        $pdf_convert->save();
    }

    protected function addHttp($url)
    {
        // return $url;
        if (empty($url)) {
            return '';
        }

        // Search the pattern
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            // If not exist then add http
            $url = 'https://' . $url;
        }

        // Return the URL
        return $url;
    }

    public function convertUV3DToPdf($pdf_convert)
    {
        $orderItemBarcodes = SaleOrderItemBarcode::getItemsToConvertPdf($pdf_convert->id);

        if (!$orderItemBarcodes->count()) {
            echo "bug error item == 0\n";
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $product = Product::find($pdf_convert->product_id);

        if (!$product) {
            echo "bug error product not found\n";
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $printingLayoutTemplate = $product->printingLayoutTemplate($pdf_convert->print_method)->first();

        if (!$printingLayoutTemplate) {
            echo "bug error product not found\n";
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $platen_size = explode('x', $printingLayoutTemplate->platen_size);
        $front_size = explode('x', $printingLayoutTemplate->front_size);
        $background_color = $printingLayoutTemplate->background_color;
        $background_color = is_null($background_color) ? 'white' : $background_color;
        $name = $product->style . '-' . $product->size . 'Z-' . $product->color;
        $outputs = [];
        $idConvertSuccess = [];
        $idConvertFail = [];
        $convert_percent = 0;
        $mugs_path = storage_path('app/public/3d');
        $maxItemOnRow = $printingLayoutTemplate->max_item_on_row;
        $maxRow = $printingLayoutTemplate->max_row;
        $elementSpace = $printingLayoutTemplate->margin_x; // px
        $widthItem = ((float) $platen_size[0]) * self::DPI;
        $pageWidthPt = $printingLayoutTemplate->page_width_pt;
        $layoutType = 'A'; // 9.5x3.6
        $view = $pdf_convert->print_method == BarcodePrinted::METHOD_UV3D ? 'print_3d' : 'print_3d_sticker';
        if ($view === 'print_3d_sticker' && $printingLayoutTemplate->front_size === '9.7x4') {
            $layoutType = 'B';
        }

        // $view = 'print_3d';

        try {
            if (!is_dir($mugs_path)) {
                mkdir($mugs_path, 0777, true);
            }
            $area = [
                'width_area' => ((float) $platen_size[0]) * self::DPI,
                'height_area' => ((float) $platen_size[1]) * self::DPI + 450, // QR label ID
                'width_image' => ((float) $front_size[0]) * self::DPI,
                'height_image' => ((float) $front_size[1]) * self::DPI
            ];
            $ratio = $area['width_image'] / $area['height_image'];
            $margin_right = 20; //px
            $margin_bottom = 10; //px
            $totalRow = ceil(count($orderItemBarcodes) / $maxItemOnRow);
            $pageHeightByRow = $area['height_area'] * $totalRow;
            $newOrderIds = [];
            foreach ($orderItemBarcodes as $key => $item) {
                if ($item->saleOrder->order_status == SaleOrder::NEW_ORDER) {
                    $newOrderIds[] = $item->order_id;
                }
                echo "\nsale order : {$item->saleOrder->id}" . PHP_EOL;
                // $left = self::PAGE_WIDTH_PX - $area['width_area'] * ($key % $maxItemOnRow + 1) - $margin_right * ($key % $maxItemOnRow) - 12500;
                $left = $elementSpace * ($key % $maxItemOnRow + 1) + $widthItem * ($key % $maxItemOnRow) + 127.5;

                echo "left of key $key: $left \n";
                $row = ceil(($key + 1) / $maxItemOnRow);
                $top = $pageHeightByRow - $area['height_area'] * $row - (ceil(($key + 1) / $maxItemOnRow) - 1) * $margin_bottom;

                $left = $elementSpace * ($key % $maxItemOnRow + 1) + $widthItem * ($key % $maxItemOnRow) + 127.5 + ($key % $maxItemOnRow) * 30;
                $top = floor($key / 2) * ($area['height_area'] - 400) + (floor($key / 2)) * 12;

                $qr_label_id = QrCode::size(100)->generate($item->label_id);
                $file = '/' . $pdf_convert->id . '/' . $item->orderItem->images->first()->id . '.png';
                $url = null;
                $isOptimized = false;
                $img = $item->orderItem->images->first();

                if ($img->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$img->order_date}/{$img->sku}-{$img->print_side}.png")) {
                    $url = file_get_contents(env('AWS_URL') . '/artwork/' . $img->order_date . '/' . $img->sku . '-' . $img->print_side . '.png');
                } else {
                    $url = file_get_contents($this->addHttp($img->image_url));
                }

                if (!$url || is_null($url) || (md5($url) != $img->imageHash?->hash_md5)) {
                    $idConvertFail[] = $item->id;

                    continue;
                }

                // if file size too large
                if ($img->image_size > self::MAX_SIZE_IMAGE) {
                    echo 'optimize image_id: ' . $img->id . "\n";
                    Storage::disk('public')->put('3d/' . $file, $url);

                    if (!Storage::disk('public')->exists('3d/' . $file)) {
                        echo "bug download failed \n";
                        $idConvertFail[] = $item->id;

                        continue;
                    }

                    // optimize image
                    $this->optimizeImage($mugs_path . $file);
                    $isOptimized = true;
                }

                // $imageSize = $this->checkImageSize($front_size, $item);
                $image_width = ((float) $front_size[0]) * self::DPI;
                $image_height = ((float) $front_size[1]) * self::DPI;
                $imageOptimize = $isOptimized ? file_get_contents($mugs_path . '/' . $pdf_convert->id . '/' . $img->id . '.png') : $url;
                $hardGoodAndShirt = resolve(SaleOrderRepository::class)->getHardGoodAndShirt($item->order_id);
                $isHardGoodAndShirt = count($hardGoodAndShirt) >= 2;
                $shirtQuantity = $isHardGoodAndShirt ? $hardGoodAndShirt[0] : null;

                echo "width of key $key:" . $image_width . PHP_EOL;
                echo "height of key $key:" . $image_height . PHP_EOL;

                $outputs[] = [
                    'isHardGoodAndShirt' => $isHardGoodAndShirt,
                    'shirtQuantity' => $shirtQuantity,
                    'label' => $item->label_id,
                    'qrCode' => '<img class="qr" src="data:image/svg+xml;base64,' . base64_encode($qr_label_id) . '"  width="90" height="90" />',
                    'image' => 'data:image/png;base64, ' . base64_encode($imageOptimize),
                    'width' => $image_width,
                    'height' => $image_height,
                    'top' => ($area['height_area'] - 450 - $image_height) / 2,
                    'topPs' => $top,
                    'leftPs' => $left,
                    'name' => $name,
                ];
                unset($imageOptimize);

                $convert_percent++;
                $idConvertSuccess[] = $item->id;
            }

            $log_path = storage_path('logs/ormt/' . $pdf_convert->id . '/');

            if (!is_dir($log_path)) {
                mkdir($log_path, 0777, true);
            }

            $dataOptions = [
                'data' => $outputs,
                'area' => $area,
                'name' => $name,
                'layoutType' => $layoutType,
            ];
            $pdfPaper = [0, 0, $pageWidthPt, (((float) $platen_size[1]) * self::RATIO_IN_TO_POINTS + 108) * $totalRow];

            if (!empty(env('APP_DEBUG'))) {
                $uuid = Str::uuid();
                Log::info("ConvertService.convertUV3DToPdf print_3d data options uuid.$uuid", $dataOptions);
                Log::info("ConvertService.convertUV3DToPdf print_3d paper options uuid.$uuid", $pdfPaper);
            }

            $htmlPath = $mugs_path . '/' . 'result.html';
            File::put($htmlPath,
                view($view)
                    ->with($dataOptions)
                    ->render(),
            );
            $fileWk = $mugs_path . '/' . 'wk.png';
            $fileIm = $mugs_path . '/' . 'im.png';
            exec("wkhtmltoimage --transparent --format png $htmlPath $fileWk");
            exec("convert $fileWk -set units pixelsperinch -density 300 $fileIm");
            echo 'Step 3: ' . memory_get_usage() . PHP_EOL;
            $s3 = Storage::disk('s3')->put('/3d/' . $pdf_convert->id . '.png', file_get_contents($fileIm));

            $pdfFile = $mugs_path . '/result.pdf';

            exec("convert $fileIm $pdfFile");

            Storage::disk('s3')->put('/3d/' . $pdf_convert->id . '.pdf', file_get_contents($pdfFile));

            if (is_dir($mugs_path . '/' . $pdf_convert->id)) {
                File::deleteDirectory($mugs_path . '/' . $pdf_convert->id);
            }

            if (file_exists($htmlPath)) {
                File::delete($htmlPath);
            }

            if (file_exists($fileWk)) {
                File::delete($fileWk);
            }

            if (file_exists($fileIm)) {
                File::delete($fileIm);
            }

            if (file_exists($pdfFile)) {
                unlink($pdfFile);
            }

            $pdf = null;

            DB::beginTransaction();
            $pdf_convert->convert_percent = $convert_percent;
            $pdf_convert->convert_status = BarcodePrinted::ACTIVE;
            $pdf_convert->converted_at = Carbon::now();
            $pdf_convert->quantity = $orderItemBarcodes->count();
            $pdf_convert->save();

            SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => $pdf_convert->id,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_SUCCESS,
                        'employee_pull_id' => $pdf_convert->employee_id,
                        'print_barcode_at' => Carbon::now(),
                        //   'pulled_at' => null // Carbon::now()
                    ],
                );

            SaleOrderItemBarcode::whereIn('id', $idConvertFail)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_FAIL,
                        'retry_convert' => DB::raw('retry_convert+1')
                    ],
                );

            // Update barcode printed status
            $saleOrderIds = SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)->pluck('order_id')->toArray();
            SaleOrder::whereIn('id', array_unique($saleOrderIds))
                ->where('order_status', SaleOrder::NEW_ORDER)
                ->update(
                    [
                        'order_status' => SaleOrder::IN_PRODUCTION,
                        'order_production_at' => Carbon::now()
                    ],
                );

            if (!empty($newOrderIds)) {
                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, array_unique($newOrderIds));
            }

            echo 'done ==> pdf_converted_id = ' . $pdf_convert->id . "\n";
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertFail($pdf_convert);
        }
    }

    public function convertPosterWithLayerCut($pdf_convert)
    {
        $orderItemBarcodes = SaleOrderItemBarcode::getItemsToConvertPdf($pdf_convert->id);

        if (!$orderItemBarcodes->count()) {
            echo "bug error item == 0\n";
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $product = Product::findByIdAndPresetSku($pdf_convert->product_id);

        if (!$product) {
            $message = "barcodePrintedId $pdf_convert->id : bug error preset not found, stop\n";
            echo $message;
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $posterSize = $product->printingPresetSku->poster_size;

        if (!$posterSize) {
            $message = "barcodePrintedId $pdf_convert->id : bug error preset poster not found, stop\n";
            echo $message;
            $this->updateConvertFail($pdf_convert);

            return false;
        }
//        $posterSize = '36x24';
        var_dump($posterSize);
        $posterConfigs = DB::table('poster_layout_config')
            ->where('size', $posterSize)
            ->first();
        if (!$posterConfigs) {
            $message = "barcodePrintedId $pdf_convert->id : bug error setting layout poster not found, stop\n";
            echo $message;
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $config = json_decode($posterConfigs->config, true);
        $maxColumn = $config['maxColumn'];
        $begin = $config['begin'] * self::DPI;
        $margin = $config['margin'] * self::DPI;
        $topQr = $config['topQr'] * self::DPI;
        $topImage = $config['topImage'] * self::DPI;
        $paperOptions = $config['paperOptions'];
        $isRotateImage = $config['isRotateImage'];
        $isRotateQr = $config['isRotateQr'] ?? $isRotateImage;
        $cornerPositions = $config['cornerPositions'];

        $posterSizeArr = explode('x', $posterSize);
        $name = $product->style . '-' . $product->size . 'Z-' . $product->color;
        $idConvertSuccess = [];
        $convert_percent = 0;
        $poster_path = storage_path('app/public/poster/');

        $printAreaWidth = ($isRotateImage ? $posterSizeArr[1] : $posterSizeArr[0]) * self::DPI;
        $printAreaHeight = ($isRotateImage ? $posterSizeArr[0] : $posterSizeArr[1]) * self::DPI;
        $imageWidth = $posterSizeArr[0] * self::DPI;
        $imageHeight = $posterSizeArr[1] * self::DPI;
        $pointToInch = 72;

        if ($orderItemBarcodes->count() === 1 && $maxColumn > 1) {
            $offset = $printAreaWidth * ($maxColumn - 1) + $margin * ($maxColumn - 1);
            $rightPosition = ($paperOptions[2] / $pointToInch * self::DPI) - $offset;
            foreach (['top_right', 'bottom_right'] as $position) {
                $cornerPositions[$position]['x_start'] -= $offset;
                $cornerPositions[$position]['y_start'] -= $offset;
            }
            $paperOptions[2] = $rightPosition / self::DPI * $pointToInch;
        }

        $outputs = [];
        try {
            if (!is_dir($poster_path)) {
                mkdir($poster_path, 0777, true);
            }

            // Lấy item đầu tiên trong $orderItemBarcodes
//            $firstItemValue = $orderItemBarcodes->first();
//            $newItems = collect();
//            for ($i = 0; $i < $maxColumn; $i++) {
//                $newItems->push($firstItemValue);
//            }
//            $orderItemBarcodes = $newItems;

            foreach ($orderItemBarcodes as $key => $saleOrderItemBarcode) {
                echo "\nsale order : {$saleOrderItemBarcode->saleOrder->id}" . PHP_EOL;
                $qr_label_id = QrCode::size(100)->generate($saleOrderItemBarcode->label_id);
                $img = $saleOrderItemBarcode->orderItem->images->first();
                $hardGoodAndShirt = resolve(SaleOrderRepository::class)->getHardGoodAndShirt($saleOrderItemBarcode->saleOrder->id);
                $isHardGoodAndShirt = count($hardGoodAndShirt) >= 2;
                $shirtQuantity = $isHardGoodAndShirt ? $hardGoodAndShirt[0] : null;
                if ($img->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$img->order_date}/{$img->sku}-{$img->print_side}.png")) {
                    $url = file_get_contents(env('AWS_URL') . '/artwork/' . $img->order_date . '/' . $img->sku . '-' . $img->print_side . '.png');
                } else {
                    $url = file_get_contents($this->addHttp($img->image_url));
                }
                //  $url = file_get_contents($this->addHttp('https://swiftpod.s3.us-west-1.amazonaws.com/artwork/2025-05-19/S22485192POST1W243-0.png'));

                if (!$url || is_null($url)) {
                    echo "image null \n";
                    $this->updateConvertFail($pdf_convert);

                    return false;
                }
                //optimize image
                $imagick = new \Imagick();
                $imagick->readImageBlob($url);
                $orientation = $imagick->getImageOrientation();
                echo "orientation : {$orientation}" . PHP_EOL;

                switch ($orientation) {
                    case \Imagick::ORIENTATION_RIGHTTOP:
                        $imagick->rotateImage(new \ImagickPixel(), 90);
                        $imagick->setImageOrientation(\Imagick::ORIENTATION_TOPLEFT);
                        break;
                    case \Imagick::ORIENTATION_BOTTOMRIGHT:
                        $imagick->rotateImage(new \ImagickPixel(), 180);
                        $imagick->setImageOrientation(\Imagick::ORIENTATION_TOPLEFT);
                        break;
                    case \Imagick::ORIENTATION_LEFTBOTTOM:
                        $imagick->rotateImage(new \ImagickPixel(), -90);
                        $imagick->setImageOrientation(\Imagick::ORIENTATION_TOPLEFT);
                        break;
                }
                $imagick->stripImage();
                $imageOptimize = $imagick->getImageBlob();
                $left = $begin + ($printAreaWidth * $key) + ($margin * $key);
                if ($posterSize == '8x12') {
                    $leftValues = [
                        0 => 0.65 * self::DPI,
                        1 => 9.48 * self::DPI,
                        2 => 18.45 * self::DPI,
                        3 => 27.28 * self::DPI,
                        4 => 36.17 * self::DPI,
                        5 => 45 * self::DPI,
                    ];
                    $left = $leftValues[$key] ?? 0;
                }
                $leftQr = match ($posterSize) {
                    '16x24', '8x12' => ($left ?? 0) + 45,
                    '24x24', '27x40', '24x40', '18x24',
                    '28x28', '28x40', '8x20', '10x24',
                    '11x14', '14x14', '16x20', '24x32', '9x11', '5x7',
                    '8x10', '4x6', '12x12', '8x8', '17x17', '5x5', '12x8',
                    '11x9', '10x8', '14x11', '20x16', '20x8', '24x10', '32x24',
                    '24x16', '24x18', '40x24', '6x4', '7x5', '40x28', '40x27' => ($left ?? 0) + 90,
                    '24x36', '36x24' => ($left ?? 0) + 60,
                    '16x16' => ($left ?? 0) + 50,
                    '32x48', '48x32' => 0.6 * self::DPI,
                    '30x30' => $left + 60,
                    default => $left ?? 0,
                };
                $outputs[] = [
                    'isHardGoodAndShirt' => $isHardGoodAndShirt,
                    'shirtQuantity' => $shirtQuantity,
                    'label' => $saleOrderItemBarcode->label_id,
                    'qrCode' => '<img src="data:image/svg+xml;base64,' . base64_encode($qr_label_id) . '"  width="100" height="100" />',
                    'image' => 'data:image/png;base64, ' . base64_encode($imageOptimize),
                    'itemNumber' => $saleOrderItemBarcode->barcode_number . '/' . $saleOrderItemBarcode->order_quantity,
                    'width_area_die_cut' => $printAreaWidth + 15,
                    'height_area_die_cut' => $printAreaHeight + 15,
                    'image_width' => $imageWidth + 15,
                    'image_height' => $imageHeight + 15,
                    'left' => $left,
                    'leftQr' => $leftQr,
                ];
                $idConvertSuccess[] = $saleOrderItemBarcode->id;
                $convert_percent++;
            }
            $options = [
                'data' => $outputs,
                'name' => $name,
                'cornerPositions' => $cornerPositions,
                'topQr' => $topQr,
                'topImage' => $topImage,
                'isRotateImage' => $isRotateImage,
                'isRotateQr' => $isRotateQr,
            ];
            $poster_path = storage_path('app/public/');
            $pdfFilePath = $poster_path . '/poster.pdf';  // Original PDF generated by Pdf::loadView
            $log_path = storage_path('logs/ormt/' . $pdf_convert->id . '/');
            if (!is_dir($log_path)) {
                mkdir($log_path, 0777, true);
            }

            $pdf = Pdf::loadView('poster_layer_cut', $options)->setOptions([
                'dpi' => self::DPI,
                'logOutputFile' => storage_path('logs/pdf.log'),
                'tempDir' => $log_path

            ])
                ->setOptions(['dpi' => 300])
                ->setPaper($paperOptions)
                ->save($pdfFilePath);  // Save the generated PDF to a file

            $path = '/poster/' . $pdf_convert->id . '.pdf';
            Storage::disk('s3')->put($path, $pdf->output());
            $url = Storage::disk('s3')->url($path);
            echo $url;
            if (is_dir($poster_path . 'poster/' . $pdf_convert->id)) {
                File::deleteDirectory($poster_path . 'poster/' . $pdf_convert->id);
            }

            DB::beginTransaction();
            $pdf_convert->convert_percent = $convert_percent;
            $pdf_convert->convert_status = BarcodePrinted::ACTIVE;
            $pdf_convert->converted_at = Carbon::now();
            $pdf_convert->quantity = $orderItemBarcodes->count();
            $pdf_convert->save();

            SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => $pdf_convert->id,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_SUCCESS,
                        'employee_pull_id' => $pdf_convert->employee_id,
                        'print_barcode_at' => Carbon::now(),
                    ],
                );

            // Update barcode printed status
            $orderIds = SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)
                ->whereHas('saleOrder', function ($query) {
                    $query->where('order_status', SaleOrder::NEW_ORDER);
                })
                ->with('saleOrder:id,order_status') // Chỉ lấy các cột cần thiết
                ->get()
                ->pluck('saleOrder.id')
                ->unique()
                ->toArray();
            if (!empty($orderIds)) {
                SaleOrder::whereIn('id', $orderIds)
                    ->update([
                        'order_status' => SaleOrder::IN_PRODUCTION,
                        'order_production_at' => now(),
                    ]);

                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $orderIds);
            }
            echo 'done ==> pdf_converted_id = ' . $pdf_convert->id . "\n";
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertFail($pdf_convert);
        }
    }

    public function convertPoster($pdf_convert)
    {
        $orderItemBarcodes = SaleOrderItemBarcode::getItemsToConvertPdf($pdf_convert->id);

        if (!$orderItemBarcodes->count()) {
            echo "bug error item == 0\n";
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $product = Product::findByIdAndPresetSku($pdf_convert->product_id);

        if (!$product) {
            $message = "barcodePrintedId $pdf_convert->id : bug error preset not found, stop\n";
            echo $message;
            $this->updateConvertFail($pdf_convert);

            return false;
        }

        $platen_size = $front_size = explode('x', $product->printingPresetSku->poster_size);
        var_dump($platen_size);
        $name = $product->style . '-' . $product->size . 'Z-' . $product->color;
        $idConvertSuccess = [];
        $idConvertFail = [];
        $convert_percent = 1;
        $poster_path = storage_path('app/public/poster/');

        try {
            if (!is_dir($poster_path)) {
                mkdir($poster_path, 0777, true);
            }

            $saleOrderItemBarcode = $orderItemBarcodes->first();
            echo "\nsale order : {$saleOrderItemBarcode->saleOrder->id}" . PHP_EOL;
            $qr_label_id = QrCode::size(100)->generate($saleOrderItemBarcode->label_id);
            $img = $saleOrderItemBarcode->orderItem->images->first();
            $hardGoodAndShirt = resolve(SaleOrderRepository::class)->getHardGoodAndShirt($saleOrderItemBarcode->saleOrder->id);
            $isHardGoodAndShirt = count($hardGoodAndShirt) >= 2;
            $shirtQuantity = $isHardGoodAndShirt ? $hardGoodAndShirt[0] : null;

            if ($img->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$img->order_date}/{$img->sku}-{$img->print_side}.png")) {
                $url = file_get_contents(env('AWS_URL') . '/artwork/' . $img->order_date . '/' . $img->sku . '-' . $img->print_side . '.png');
            } else {
                $url = file_get_contents($this->addHttp($img->image_url));
            }

            if (!$url || is_null($url)) {
                echo "image null \n";
                $this->updateConvertFail($pdf_convert);

                return false;
            }

            $widthItem = ((float) $platen_size[0]) * self::DPI;
            $heightItem = ((float) $platen_size[1]) * self::DPI;

            $widthSafeZone = ((float) $front_size[0]) * self::DPI;
            $heightSafeZone = ((float) $front_size[1]) * self::DPI;

            // optimize image
            $imagick = new \Imagick();
            $imagick->readImageBlob($url);
            $orientation = $imagick->getImageOrientation();
            echo "orientation : {$orientation}" . PHP_EOL;

            switch ($orientation) {
                case \Imagick::ORIENTATION_RIGHTTOP:
                    $imagick->rotateImage(new \ImagickPixel(), 90);
                    $imagick->setImageOrientation(\Imagick::ORIENTATION_TOPLEFT);
                    break;
                case \Imagick::ORIENTATION_BOTTOMRIGHT:
                    $imagick->rotateImage(new \ImagickPixel(), 180);
                    $imagick->setImageOrientation(\Imagick::ORIENTATION_TOPLEFT);
                    break;
                case \Imagick::ORIENTATION_LEFTBOTTOM:
                    $imagick->rotateImage(new \ImagickPixel(), -90);
                    $imagick->setImageOrientation(\Imagick::ORIENTATION_TOPLEFT);
                    break;
            }

            $imagick->stripImage();
            $file = $imagick->getImageBlob();
            $fileString = 'data:image/png;base64, ' . base64_encode($file);
            $increase = 0.04 * self::DPI; // tang size anh len 0.04in in theo request ticket #TIC-001853
            $images = [
                'isHardGoodAndShirt' => $isHardGoodAndShirt,
                'shirtQuantity' => $shirtQuantity,

                'image' => $fileString,
                'width_safe_zone' => $widthSafeZone + $increase,
                'height_safe_zone' => $heightSafeZone + $increase,

                'width_image' => $widthSafeZone + $increase,
                'height_image' => $heightSafeZone + $increase,

                'width_item' => $widthItem + $increase,
                'height_item' => $heightItem + $increase,
                'qrCode' => '<img src="data:image/svg+xml;base64,' . base64_encode($qr_label_id) . '"  width="150" height="150" />',
                'label' => $saleOrderItemBarcode->label_id,
            ];

            print_r([
                'width_safe_zone' => $widthSafeZone,
                'height_safe_zone' => $heightSafeZone,

                'width_image' => $widthSafeZone,
                'height_image' => $heightSafeZone,

                'width_item' => $widthItem,
                'height_item' => $heightItem,
                'label' => $saleOrderItemBarcode->label_id,
            ]);
            $idConvertSuccess[] = $saleOrderItemBarcode->id;
            app()->make('view.engine.resolver')->register('blade', function () {
                return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
            });
            $htmlPath = $poster_path . 'result.html';
            $dataOptions = ['image' => $images, 'name' => $name];
            !empty(env('APP_DEBUG')) && Log::info('ConvertService.convertPoster poster data options', $dataOptions);
            File::put($htmlPath,
                view('poster')
                    ->with($dataOptions)
                    ->render(),
            );
            $fileWk = $poster_path . 'wk.png';
            $fileIm = $poster_path . 'im.png';
            exec("wkhtmltoimage --transparent --format png $htmlPath $fileWk");
            exec("convert $fileWk -set units pixelsperinch -density 300 $fileIm");
            echo 'Step 3: ' . memory_get_usage() . PHP_EOL;
            $s3 = Storage::disk('s3')->put('/poster/' . $pdf_convert->id . '.png', file_get_contents($fileIm));
            $pxToPt = self::DPI / self::RATIO_IN_TO_POINTS;
            $widthFrame = getimagesize($fileIm)[0] / $pxToPt;
            $heightFrame = getimagesize($fileIm)[1] / $pxToPt;
            $positionIllus['id'] = $pdf_convert->id;
            $positionIllus['is_convert_poster'] = 1;
            $positionIllus['width_die_cut'] = $widthSafeZone / $pxToPt;
            $positionIllus['height_die_cut'] = $heightSafeZone / $pxToPt;
            $positionIllus['width'] = $widthFrame;
            $positionIllus['height'] = $heightFrame;
            $positionIllus['position'][] = [
                'top' => ($heightFrame - ($heightSafeZone / $pxToPt)) / 2 + 100 / $pxToPt + 2.63,
                'left' => ($widthFrame - ($widthSafeZone / $pxToPt)) / 2 - (0.02 * 72)
            ];
            print_r($positionIllus);

            if (file_exists($htmlPath)) {
                File::delete($htmlPath);
            }

            if (file_exists($fileWk)) {
                File::delete($fileWk);
            }

            if (file_exists($fileIm)) {
                File::delete($fileIm);
            }

            DB::beginTransaction();
            $pdf_convert->convert_percent = $convert_percent;
            $pdf_convert->convert_status = BarcodePrinted::ACTIVE;
            $pdf_convert->converted_at = Carbon::now();
            $pdf_convert->quantity = 1;
            $pdf_convert->positions = json_encode($positionIllus);
            $pdf_convert->save();

            SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => $pdf_convert->id,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_SUCCESS,
                        'employee_pull_id' => $pdf_convert->employee_id,
                        'print_barcode_at' => Carbon::now(),
                    ],
                );

            SaleOrderItemBarcode::whereIn('id', $idConvertFail)
                ->where('is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->update(
                    [
                        'barcode_printed_id' => BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT,
                        'convert_pdf_status' => SaleOrderItemBarcode::CONVERT_PDF_FAIL,
                        'retry_convert' => DB::raw('retry_convert+1')
                    ],
                );

            // Update barcode printed status
            $saleOrderIds = SaleOrderItemBarcode::whereIn('id', $idConvertSuccess)->pluck('order_id')->toArray();
            SaleOrder::whereIn('id', array_unique($saleOrderIds))
                ->where('order_status', SaleOrder::NEW_ORDER)
                ->update(
                    [
                        'order_status' => SaleOrder::IN_PRODUCTION,
                        'order_production_at' => Carbon::now()
                    ],
                );

            if ($saleOrderItemBarcode->saleOrder->order_status == SaleOrder::NEW_ORDER) {
                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, array_unique($saleOrderIds));
            }

            echo 'done ==> pdf_converted_id = ' . $pdf_convert->id . "\n";
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertFail($pdf_convert);
        }
    }

    private function resizeImageByRatio($url, $width_area_safe, $height_area_safe, $original_width, $original_height)
    {
        $img = Image::make($url);
        $ratio = min($width_area_safe / $original_width, $height_area_safe / $original_height);
        if ($original_width > $width_area_safe || $original_height > $height_area_safe) {
            $new_width = intval($original_width * $ratio);
            $new_height = intval($original_height * $ratio);
            $img->resize($new_width, $new_height, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        return $img;
    }
}
