<?php

namespace App\Http\Requests\SaleOrder;

use App\Models\Tag;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddTagRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $tags = Tag::tagRestrictRemove()->pluck('id')
            ->merge(collect([Tag::LABEL_TAG_ID, Tag::TAG_RBT_ID, Tag::TAG_16x18_ID]))
            ->unique()
            ->toArray();

        return [
            'order_ids' => [
                'required',
                'array',
                'min:1',
            ],
            'order_ids.*' => [
                'distinct',
                'exists:sale_order,id',
            ],
            'tags' => [
                'required',
                'array',
                'min:1',
            ],
            'tags.*' => [
                'distinct',
                'exists:tag,id',
                Rule::notIn($tags)
            ],
        ];
    }
}
