<?php

namespace App\Http\Controllers;

use App\Http\Requests\SupplyLocation\CreateSupplyLocation;
use App\Http\Requests\SupplyLocation\UpdateSupplyLocation;
use App\Repositories\SupplyLocationRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SupplyLocationController extends Controller
{
    public function __construct(protected SupplyLocationRepository $supplyLocationRepository)
    {
    }

    public function fetch(Request $request)
    {
        $input = $request->only(['barcode', 'limit', 'page', 'is_fetch_all']);
        $input['warehouse_id'] = config('jwt.warehouse_id');
        $res = $this->supplyLocationRepository->fetch($input);

        return response()->json($res, Response::HTTP_OK);
    }

    public function create(CreateSupplyLocation $request)
    {
        $input = $request->validated();
        $input['warehouse_id'] = config('jwt.warehouse_id');

        if (empty($input['items'])) {
            return response()->json(
                ['message' => 'Invalid or empty params!'],
                Response::HTTP_NOT_FOUND,
            );
        }

        return $this->supplyLocationRepository->create($input);
    }

    public function update(UpdateSupplyLocation $request, $id)
    {
        $input = $request->validated();
        if (empty($input)) {
            return response()->json(
                ['message' => 'Invalid or empty params!'],
                Response::HTTP_NOT_FOUND,
            );
        }

        return $this->supplyLocationRepository->update($id, $input);
    }

    public function delete($id)
    {
        return $this->supplyLocationRepository->delete($id);
    }
}
