<?php

namespace App\Http\Controllers;

use App\Repositories\SupplyBoxRepository;
use Illuminate\Http\Request;

class SupplyBoxController extends Controller
{
    public function __construct(protected SupplyBoxRepository $supplyBoxRepository)
    {
    }

    public function fetchBox(Request $request)
    {
        $input = $request->only([
            'page',
            'limit',
            'supply_name',
            'supply_sku',
            'box_id',
            'location_id',
            'is_fetch_all'
        ]);

        $supplyBoxes = $this->supplyBoxRepository->fetchBox($input);

        return response()->json($supplyBoxes);
    }

    public function getBoxes(Request $request)
    {
        $input = $request->only([
            'supply_sku'
        ]);
        $supplyBoxes = $this->supplyBoxRepository->getBoxes($input);

        return response()->json($supplyBoxes);
    }

    public function getBoxInfo(Request $request)
    {
        $input = $request->only([
            'barcode'
        ]);
        $input['warehouse_id'] = config('jwt.warehouse_id');
        $supplyBoxes = $this->supplyBoxRepository->getBoxInfo($input);

        return response()->json($supplyBoxes);
    }
}
