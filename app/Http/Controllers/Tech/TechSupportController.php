<?php

namespace App\Http\Controllers\Tech;

use App\Console\Commands\EmployeeWorkLogs\CalculateEmployeeWorkLogTrait;
use App\Http\Controllers\Controller;
use App\Jobs\CreateThumbArtwork;
use App\Jobs\DetectColorArtwork;
use App\Jobs\DetectShippingMethodRedbubbleV2Job;
use App\Jobs\GetRateShipXJob;
use App\Jobs\UploadArtworkToS3;
use App\Models\PurchaseOrder;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderItemImage;
use App\Models\Shipment;
use App\Models\StoreCallbackUrl;
use App\Models\StoreIntegrate;
use App\Repositories\TechRepository;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class TechSupportController extends Controller
{
    use CalculateEmployeeWorkLogTrait;

    public function __construct(protected TechRepository $techRepository)
    {
    }

    public function updateLabelUrl(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->merge(['id' => $id])->all(), [
            'id' => 'required|integer|exists:shipment,id',
            'label_url' => 'required|string|url',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->updateLabelUrl($validator->validated());
    }

    public function updateIPViolationImage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'images_hash' => 'required|array',
            'images_hash.*' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->updateIPViolationImage($validator->validated());
    }

    public function markIPViolationImage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'images_hash' => 'required|array',
            'images_hash.*' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->markIPViolationImage($validator->validated());
    }

    public function retryVisuaDetectImageBySessionId(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'visua_session_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->retryVisuaDetectImageBySessionId($validator->validated());
    }

    public function updateOrderStatus(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->merge(['id' => $id])->all(), [
            'id' => 'required|integer|exists:sale_order,id',
            'status' => ['required', 'string', Rule::in([SaleOrder::SHIPPED, SaleOrder::NEW_ORDER, SaleOrder::CANCELLED, SaleOrder::DRAFT, SaleOrder::REJECTED, SaleOrder::ON_HOLD, SaleOrder::IN_PRODUCTION, SaleOrder::MANUAL_PROCESS, SaleOrder::STATUS_LATE_CANCELLED])],
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->updateOrderStatus($validator->validated());
    }

    public function insertProductTiktok(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'skus' => 'required|array',
            'skus.*' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->insertProductTiktok($validator->validated());
    }

    public function reGenerateInvoice(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'invoice_ids' => 'required|array',
            'invoice_ids.*' => 'integer|exists:invoices,id',
            'type' => 'required|string|in:general,production,shipping,insert',
            'is_error' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->techRepository->reGenerateInvoice($validator->validated());
    }

    public function detectShippingMethodRb(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        foreach ($validator->validated()['order_id'] as $orderId) {
            DetectShippingMethodRedbubbleV2Job::dispatch($orderId)->onQueue(SaleOrder::JOB_DETECT_SHIPPING_METHOD_REDBUBBLE_V2);
        }

        return true;
    }

    public function updatePurchaseOrder($id, Request $request): JsonResponse
    {
        $purchaseOrder = PurchaseOrder::find($id);

        if (!$purchaseOrder) {
            return response()->json(['message' => 'po not found'], 422);
        }

        if (!empty($request->order_status)) {
            if (!in_array($request->order_status, [
                PurchaseOrder::COMPLETED_STATUS,
                PurchaseOrder::PARTIAL_RECEIVED_STATUS
            ])) {
                return response()->json(['message' => 'order status invalid'], 422);
            }

            $purchaseOrder->order_status = $request->order_status;
            $purchaseOrder->save();
        }

        return response()->json(['message' => 'update success'], 200);
    }

    public function alertQc()
    {
        try {
            setTimezone();
            $endDate = Carbon::now()->endOfDay();
            $startDate = Carbon::now()->subDays(7)->startOfDay();

            // Base query
            $latestStatusSubquery = DB::table('sale_order_item_quality_control')
            ->select('order_item_id', 'status as latest_status')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereIn(DB::raw('(order_item_id, created_at)'), function ($query) use ($startDate, $endDate) {
                $query->select('order_item_id', DB::raw('MAX(created_at)'))
                    ->from('sale_order_item_quality_control')
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->groupBy('order_item_id');
            });

            // Base query
            $results = DB::table('sale_order_item_quality_control as qc')
                ->join('sale_order_item as soi', 'soi.id', '=', 'qc.order_item_id')
                ->join('sale_order as so', 'so.id', '=', 'soi.order_id')
                ->join('store as s', 's.id', '=', 'so.store_id')
                ->join('product_quantity as pq', 'pq.product_id', '=', 'soi.product_id')
                ->joinSub($latestStatusSubquery, 'lc', function ($join) {
                    $join->on('lc.order_item_id', '=', 'soi.id');
                })
                ->select(
                    's.code',
                    'so.order_number',
                    'so.warehouse_id',
                    'so.created_at as order_created_at',
                    'so.order_status',
                    'soi.sku',
                    'soi.id as order_item_id',
                    'lc.latest_status as max_status',
                    DB::raw('CASE
                    WHEN qc.label_id LIKE "%(%" THEN LEFT(qc.label_id, LENGTH(qc.label_id) - 3)
                    ELSE qc.label_id
                END as label'),
                    'soi.id',
                    DB::raw('MAX(pq.quantity) as quantity'),
                    DB::raw('MAX(pq.incoming_stock) as incoming_stock'),
                    DB::raw('MAX(qc.created_at) as qc_created_at'),
                    DB::raw('MAX(qc.image) as proof'),
                    DB::raw('COUNT(DISTINCT CASE WHEN qc.status != "pass" THEN qc.id END) as qc_issue_count'),
                    DB::raw('MIN(qc.label_id) as label_id'),
                )
                ->whereBetween('qc.created_at', [$startDate, $endDate])
                ->groupBy(
                    's.code',
                    'so.order_number',
                    'so.warehouse_id',
                    'so.created_at',
                    'so.order_status',
                    'soi.sku',
                    'label',
                    'lc.latest_status',
                )
                ->havingRaw('qc_issue_count >= 3 AND max_status != "pass"')
                ->get();
            if (count($results) > 0) {
                foreach ($results as $item) {
                    handleJob(SaleOrder::JOB_SEND_MAIL_ALERT_FAILED_TIMES, $item->label);
                }
            }
        } catch (\Exception $e) {
            jobEcho('Exception caught: ' . $e->getMessage());
        }
    }

    public function dispatchUpdateOrderStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        foreach ($validator->validated()['order_id'] as $orderId) {
            handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $orderId);
        }

        return response()->json(['message' => 'dispatch update order status successfully.'], 200);
    }

    public function calculateWorkedHour(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_time' => 'required|date',
            'end_time' => 'required|date'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $startTime = Carbon::parse($request['start_time'])->startOfDay()->toDateTimeString();
        $endTime = Carbon::parse($request['end_time'])->endOfDay()->toDateTimeString();
        $jobTypes = [
            'printing' => 'dragon_fruit_print',
            'pretreat_printing' => 'all_in_one_print',
            'neck_printing' => 'neck_print',
            'create_shipment_label' => 'label',
            'folding' => 'folding',
            'pulling' => 'pulling',
            'quality_control' => 'qc',
        ];
        foreach ($jobTypes as $timeTrackingJobType => $workLogTaskType) {
            $this->updateWorkHours($timeTrackingJobType, $workLogTaskType, $startTime, $endTime);
        }

        return response()->json(['message' => 'successfully.'], 200);
    }

    public function dispatchOrderGelato($id, Request $request): JsonResponse
    {
        $saleOrder = SaleOrder::find($id);

        $dataPostBack = [
            'order_id' => $id,
            'event' => StoreCallbackUrl::ORDER_NOTIFY_EVENT,
        ];

        if ($saleOrder->order_status == SaleOrder::STATUS_SHIPPED) {
            $shipment = Shipment::where('order_id', $saleOrder->id)->first();
            $dataPostBack['data_send_gelato'] = [
                'timestamp' => Carbon::now()->toIso8601String(),
                'orderId' => $saleOrder->external_number,
                'status' => 'shipped',
                'message' => 'The order has been shipped',
                'trackingCode' => $shipment->tracking_number ?? '',
                'trackingLink' => $shipment->url_tracking_easypost ?? '',
            ];
        }

        if ($saleOrder->order_status == SaleOrder::STATUS_REJECT) {
            $dataPostBack['data_send_gelato'] = [
                'timestamp' => Carbon::now()->toIso8601String(),
                'orderId' => $saleOrder->external_number,
                'status' => 'cancelled',
                'message' => str_replace('_', ' ', $saleOrder->rejected_reason),
            ];
        }

        handleJob(StoreIntegrate::JOB_POST_BACK_GELATO, $dataPostBack);

        return response()->json(['message' => 'dispatch order successfully.'], 200);
    }

    public function dispatchOrderShippedRB(): JsonResponse
    {
        $saleOrders = DB::table('integrate_callback_log as main')
            ->where('data', 'LIKE', '%"status":"shipped"%')
            ->where('event', 'order_notify')
            ->where('store_id', 484174)
            ->where('status', 0)
            ->whereNotIn('order_id', function ($query) {
                $query->select('order_id')
                    ->from('integrate_callback_log')
                    ->where('data', 'LIKE', '%"status":"shipped"%')
                    ->where('event', 'order_notify')
                    ->where('store_id', 484174)
                    ->where('status', 1);
            })
            ->groupBy('order_id')
            ->pluck('order_id'); // Lấy danh sách order_id

        // Kiểm tra nếu không có đơn hàng nào
        if ($saleOrders->isEmpty()) {
            return response()->json(['message' => 'No orders to dispatch.'], 200);
        }

        foreach ($saleOrders as $saleOrder) {
            try {
                handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $saleOrder);
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage()], 422);
            }
        }

        return response()->json(['message' => 'Dispatch order successfully.'], 200);
    }

    public function dispatchOrderIdShippedRB(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        foreach ($validator->validated()['order_id'] as $orderId) {
            handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $orderId);
        }

        return response()->json(['message' => 'Dispatch order successfully.'], 200);
    }

    public function deleteShipmentLabelS3(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|array',
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }
            $deletedFiles = [];
            $notFoundFiles = [];

            foreach ($validator->validated()['id'] as $shipmentId) {
                $filePath = "/label/$shipmentId.pdf";
                if (Storage::disk('s3')->exists($filePath)) {
                    Storage::disk('s3')->delete($filePath);
                    $deletedFiles[] = $shipmentId;
                } else {
                    $notFoundFiles[] = $shipmentId;
                }
            }

            return response()->json([
                'message' => 'Delete operation completed.',
                'deleted_files' => $deletedFiles,
                'not_found_files' => $notFoundFiles,
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 422);
        }
    }

    public function updateShipmentUrlForStoreHPLG(): JsonResponse
    {

       $ship = new GetRateShipXJob(8240461);
        $ship->handle();
        dd(1111);
    }

    public function dispatchImageForCreateThumb($id): JsonResponse
    {
        $image = SaleOrderItemImage::find($id);

        if (!$image) {
            return response()->json(['message' => 'Image not found.'], 422);
        }

        dispatch(new CreateThumbArtwork((int) $id))->onQueue(QueueJob::QUEUE_CREATE_THUMB_ARTWORK);

        return response()->json(['message' => 'Dispatch image successfully.'], 200);
    }

    public function dispatchImageForDetectColor($id): JsonResponse
    {
        $image = SaleOrderItemImage::find($id);

        if (!$image) {
            return response()->json(['message' => 'Image not found.'], 422);
        }

        dispatch(new DetectColorArtwork((int) $id))->onQueue(QueueJob::QUEUE_DETECT_COLOR_ARTWORK);

        return response()->json(['message' => 'Dispatch image successfully.'], 200);
    }

    public function dispatchImageForUploadS3($id): JsonResponse
    {
        $image = SaleOrderItemImage::find($id);

        if (!$image) {
            return response()->json(['message' => 'Image not found.'], 422);
        }

        dispatch(new UploadArtworkToS3((int) $id))->onQueue(QueueJob::QUEUE_UPLOAD_S3);

        return response()->json(['message' => 'Dispatch image successfully.'], 200);
    }

    public function markIpByFileUpload(Request $request)
    {
        return $this->techRepository->markIpByFileUpload($request);

        return response()->json(['message' => 'Upload successfully.'], 200);
    }

    public function voidShipment(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'shipmentId' => ['required',
                Rule::exists('shipment', 'id')
                    ->where('is_deleted', false)
                    ->whereNull('refund_status')
            ],
            'orderId' => 'required',
            'employeeId' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        try {
            $res = $this->techRepository->voidShipment($input);
            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $input['orderId']);

            return response()->json($res);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
