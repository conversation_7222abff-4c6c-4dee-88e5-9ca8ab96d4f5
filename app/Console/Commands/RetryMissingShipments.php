<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\ShipmentLabelIssues;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class RetryMissingShipments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shipment:retry-upload-missing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-upload missing shipment files to S3.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->reprocessMissingShipments();
        $this->reprocessReadLabelError();
    }

    public function reprocessMissingShipments(): void
    {
        $this->info('Start Reprocess Missing Shipments');
        $orders = SaleOrder::query()
            ->where('order_status', SaleOrder::NEW_ORDER)
            ->where('order_type', SaleOrder::ORDER_TYPE_LABEL_ORDER)
            ->whereNull('order_number')
            ->where('created_at', '<', now('UTC')->subMinutes(30))
            ->where('created_at', '>', now('UTC')->subMonth())
            ->get();
        $total = $orders->count();

        if (empty($total)) {
            $this->warn('No missing shipment files found.');
        }

        foreach ($orders as $index => $item) {
            $this->info('Process ' . ($index + 1) . "/$total");

            if (!Storage::disk('s3')->exists("/label/$item->shipment_id.pdf")) {
                Log::error("RetryMissingShipments.reprocessMissingShipments shipment_id: $item->shipment_id");
                handleJob(Shipment::JOB_UPLOAD_S3_LABEL_SHIPPING, $item->shipment_id);
                $this->info('Dispatched');
            } else {
                $this->warn('Ignored');
            }
        }
    }

    public function reprocessReadLabelError(): void
    {
        $this->info('Start Reprocess Read Label Error');
        $shipmentErrored = ShipmentLabelIssues::query()
            ->where('issues', 'like', '%Error calling Gemini API%')
            ->where('created_at', '>', now('UTC')->subMinutes(90))
            ->get();
        $total = $shipmentErrored->count();

        if (empty($total)) {
            $this->warn('No shipment errored.');
        }

        foreach ($shipmentErrored as $index => $item) {
            $this->info('Process ' . ($index + 1) . "/$total");
            Log::error("RetryMissingShipments.reprocessReadLabelError shipment_id: $item->shipment_id");
            Artisan::call('app:read-label-order', ['--ids' => $item->shipment_id]);
            $this->info('Success!');
        }
    }
}
