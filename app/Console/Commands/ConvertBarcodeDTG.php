<?php

namespace App\Console\Commands;

use App\Http\Service\BarcodeService;
use App\Repositories\BarcodeRepository;
use Illuminate\Console\Command;

class ConvertBarcodeDTG extends Command
{
    /**
     * The name and signature of the console command.
     *
     * Ví dụ chạy: php artisan command:convert-barcode-queue-to-pdf 0 3
     *   - 0 là workerId
     *   - 3 là tổng số worker
     *
     * @var string
     */
    protected $signature = 'command:convert-barcode-dtg {workerId} {totalWorker}';

    /**
     * <PERSON>hi người dùng tạo danh sách barcode sản xuất thì tool này sẽ tạo thành file pdf để in
     *
     * @var string
     */
    protected $description = 'convert wip dtg to pdf for printing';


    protected $workerId;
    protected $totalWorker;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function convertBarcode()
    {

        $barcodeModel = new BarcodeRepository();

        $job = $barcodeModel->fetchPendingBarcodePrintedWorker($this->workerId, $this->totalWorker);
        if (!$job) {
            return false;
        }

        echo "Processing job: " . $job->id . "\n";

        $barcodeService = new BarcodeService();
        $barcodeService->convertBarcodeV5($job->id);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->workerId = (int)$this->argument('workerId');
        $this->totalWorker = (int)$this->argument('totalWorker');

        echo "Worker {$this->workerId}/{$this->totalWorker} started\n";

        while (true) {
            $this->convertBarcode();
            sleep(3);
        }
    }
}
