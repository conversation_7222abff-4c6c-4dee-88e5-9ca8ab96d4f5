<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoreSetting extends Model
{
    use HasFactory;

    protected $table = 'store_setting';

    protected $fillable = [
        'store_id',
        'key',
        'value',
        'created_at',
        'updated_at',
    ];

    const KEY_SHIPX_API_KEY = 'shipx_api_key';

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }
}
