<?php

namespace App\Exceptions;

use Exception;

class ShipXException extends Exception
{
    protected $shipmentId;
    protected $errorType;
    protected $context;

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Exception $previous = null,
        ?int $shipmentId = null,
        string $errorType = 'general',
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->shipmentId = $shipmentId;
        $this->errorType = $errorType;
        $this->context = $context;
    }

    public function getShipmentId(): ?int
    {
        return $this->shipmentId;
    }

    public function getErrorType(): string
    {
        return $this->errorType;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Create exception for shipment not found
     */
    public static function shipmentNotFound(int $shipmentId): self
    {
        return new self(
            "Shipment not found! - for shipment id: {$shipmentId}",
            404,
            null,
            $shipmentId,
            'shipment_not_found'
        );
    }

    /**
     * Create exception for store setting not found
     */
    public static function storeSettingNotFound(int $shipmentId): self
    {
        return new self(
            "Store setting not found! - for shipment id: {$shipmentId}",
            404,
            null,
            $shipmentId,
            'store_setting_not_found'
        );
    }

    /**
     * Create exception for sale order not found
     */
    public static function saleOrderNotFound(int $shipmentId, ?string $orderNumber = null): self
    {
        $message = "Can't find sale order! - for shipment id: {$shipmentId}";
        if ($orderNumber) {
            $message .= " order number: {$orderNumber}";
        }
        
        return new self(
            $message,
            404,
            null,
            $shipmentId,
            'sale_order_not_found'
        );
    }

    /**
     * Create exception for ShipX API error
     */
    public static function apiError(string $errorMessage, int $statusCode, int $shipmentId, ?string $orderNumber = null): self
    {
        $message = "ShipX error: {$errorMessage} - {$statusCode} for shipment id: {$shipmentId}";
        if ($orderNumber) {
            $message .= " order number: {$orderNumber}";
        }
        
        return new self(
            $message,
            $statusCode,
            null,
            $shipmentId,
            'api_error',
            ['api_error' => $errorMessage, 'status_code' => $statusCode]
        );
    }

    /**
     * Create exception for no rates found
     */
    public static function noRatesFound(int $shipmentId, ?string $orderNumber = null): self
    {
        $message = "ShipX error: No rate found from shipx for shipment id: {$shipmentId}";
        if ($orderNumber) {
            $message .= " order number: {$orderNumber}";
        }
        
        return new self(
            $message,
            404,
            null,
            $shipmentId,
            'no_rates_found'
        );
    }

    /**
     * Create exception for service code mismatch
     */
    public static function serviceCodeMismatch(string $serviceCode, int $shipmentId, ?string $orderNumber = null): self
    {
        $message = "ShipX error: not match service code with shipx rate {$serviceCode} for shipment id: {$shipmentId}";
        if ($orderNumber) {
            $message .= " order number: {$orderNumber}";
        }
        
        return new self(
            $message,
            404,
            null,
            $shipmentId,
            'service_code_mismatch',
            ['service_code' => $serviceCode]
        );
    }
}
