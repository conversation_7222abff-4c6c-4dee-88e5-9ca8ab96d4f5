<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mug sticker layout</title>
    @if ($layoutType === 'A')
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                height: 100%;
                margin: 0;
                padding: 213px 324px 0; /* 0.71*300 = 213, 1.08*300 = 324 */
                width: 6900px; /* 23*300 */
                box-sizing: border-box;
            }

            .wrapper {
                position: relative;
            }

            .row-container {
                position: relative;
                margin-bottom: 150px; /* 0.5*300 */
            }

            .row {
                font-size: 0;
                white-space: nowrap;
            }

            .box {
                display: inline-block;
                vertical-align: top;
                width: 3096px; /* 10.32 * 300 */
                height: 1095px; /* 3.65 * 300 */
                /*border: 1px solid #ccc;*/
                position: relative;
            }

            .gap {
                display: inline-block;
                width: 60px;
                height: 1px;
                vertical-align: top;
            }

            .box img.artwork {
                position: absolute;
                top: 6px; /* 0.02*300 */
                left: 21px; /* 0.07*300 */
                width: 2850px; /* 9.5*300 */
                height: 1080px; /* 3.6*300 */
                object-fit: cover;
                /*border: 4px solid green;*/
                box-sizing: border-box;
            }

            .dashed-line {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 2907px; /* 0.07*300 + 9.5*300 + 36 */
                width: 0;
                border-left: 2px dashed #333;
            }

            .qr {
                position: absolute;
                top: 35%;
                left: 60px;
                width: 90px;
                height: 90px;
            }

            .right-block {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 2908px;
                width: 186px;
            }

            .rotated-text-wrapper {
                position: absolute;
                top: 45%;
                left: 140px; /* khoảng cách từ QR đến text */
            }

            .rotated-text {
                font-size: 24px;
                white-space: nowrap;
                text-align: center;
                line-height: 1.4;
                transform: rotate(90deg);
                -webkit-transform: rotate(90deg);
                -ms-transform: rotate(90deg);
                transform-origin: top left;
                -webkit-transform-origin: top left;
                -ms-transform-origin: top left;

            }

            .mark {
                position: absolute;
                width: 30px;
                height: 30px;
                z-index: 10;
            }

            .mark.tl {
                top: -30px;
                left: -30px;
                border-top: 4px solid #343131;
                border-left: 4px solid #343131;
            }

            .mark.tr {
                top: -30px;
                left: 2880px;
                border-top: 4px solid #343131;
                border-right: 4px solid #343131;
            }

            .mark.bl {
                bottom: -30px;
                left: -30px;
                border-bottom: 4px solid #343131;
                border-left: 4px solid #343131;
            }

            .mark.br {
                bottom: -30px;
                left: 2880px;
                border-bottom: 4px solid #343131;
                border-right: 4px solid #343131;
            }

            .mark.tl-row-wrap, .mark.tr-row-wrap, .mark.bl-row-wrap, .mark.br-row-wrap {
                width: 210px;
                height: 210px;
                position: absolute;
                z-index: 10;
            }

            .mark.tl-row-wrap, .mark.bl-row-wrap {
                left: -87px;
            }

            .mark.tr-row-wrap, .mark.br-row-wrap {
                right: -87px;
            }

            .mark.tl-row-wrap, .mark.tr-row-wrap {
                top: -141px;
            }

            .mark.bl-row-wrap, .mark.br-row-wrap {
                bottom: -141px;
            }

            .mark.tl-row-wrap {
                border-top: 10px solid #343131;
                border-left: 10px solid #343131;
            }

            .mark.tr-row-wrap {
                border-top: 10px solid #343131;
                border-right: 10px solid #343131;
            }

            .mark.bl-row-wrap {
                border-bottom: 10px solid #343131;
                border-left: 10px solid #343131;
            }

            .mark.br-row-wrap {
                border-bottom: 10px solid #343131;
                border-right: 10px solid #343131;
            }
        </style>
    @else
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                height: 100%;
                margin: 0;
                padding: 147px 261px 0; /* 0.49*300 = 147, 1.08*300 = 324 */
                width: 6900px; /* 23*300 */
                box-sizing: border-box;
            }

            .wrapper {
                position: relative;
            }

            .row-container {
                position: relative;
                margin-bottom: 150px; /* 0.5*300 */
            }

            .row {
                font-size: 0;
                white-space: nowrap;
            }

            .box {
                display: inline-block;
                vertical-align: top;
                width: 3159px; /* 10.32 * 300 */
                height: 1275px; /* 3.65 * 300 */
                /*border: 1px solid #ccc;*/
                position: relative;
            }

            .gap {
                display: inline-block;
                width: 60px; /* 0.2*300 */
                height: 1px;
                vertical-align: top;
            }

            .box img.artwork {
                position: absolute;
                top: 36px; /* 0.02*300 */
                left: 24px; /* 0.07*300 */
                width: 2910px; /* 9.5*300 */
                height: 1200px; /* 3.6*300 */
                object-fit: cover;
                /*border: 4px solid yellow;*/
                box-sizing: border-box;
            }

            .dashed-line {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 2970px; /* 0.07*300 + 9.5*300 + 36 */
                width: 0;
                border-left: 2px dashed #333;
            }

            .qr {
                position: absolute;
                top: 35%;
                left: 60px;
                width: 90px;
                height: 90px;
            }

            .right-block {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 2971px;
                width: 186px;
            }

            .rotated-text-wrapper {
                position: absolute;
                top: 45%;
                left: 140px; /* khoảng cách từ QR đến text */
            }

            .rotated-text {
                font-size: 24px;
                white-space: nowrap;
                text-align: center;
                line-height: 1.4;
                transform: rotate(90deg);
                -webkit-transform: rotate(90deg);
                -ms-transform: rotate(90deg);
                transform-origin: top left;
                -webkit-transform-origin: top left;
                -ms-transform-origin: top left;

            }

            .mark {
                position: absolute;
                width: 30px;
                height: 30px;
                z-index: 10;
            }

            .mark.tl {
                top: -30px;
                left: -30px;
                border-top: 4px solid #343131;
                border-left: 4px solid #343131;
            }

            .mark.tr {
                top: -30px;
                left: 2943px;
                border-top: 4px solid #343131;
                border-right: 4px solid #343131;
            }

            .mark.bl {
                bottom: -30px;
                left: -30px;
                border-bottom: 4px solid #343131;
                border-left: 4px solid #343131;
            }

            .mark.br {
                bottom: -30px;
                left: 2943px;
                border-bottom: 4px solid #343131;
                border-right: 4px solid #343131;
            }

            .mark.tl-row-wrap, .mark.tr-row-wrap, .mark.bl-row-wrap, .mark.br-row-wrap {
                width: 210px;
                height: 210px;
                position: absolute;
                z-index: 10;
            }

            .mark.tl-row-wrap, .mark.bl-row-wrap {
                left: -117px;
            }

            .mark.tr-row-wrap, .mark.br-row-wrap {
                right: -117px;
            }

            .mark.tl-row-wrap, .mark.tr-row-wrap {
                top: -138px;
            }

            .mark.bl-row-wrap, .mark.br-row-wrap {
                bottom: -141px;
            }

            .mark.tl-row-wrap {
                border-top: 10px solid #343131;
                border-left: 10px solid #343131;
            }

            .mark.tr-row-wrap {
                border-top: 10px solid #343131;
                border-right: 10px solid #343131;
            }

            .mark.bl-row-wrap {
                border-bottom: 10px solid #343131;
                border-left: 10px solid #343131;
            }

            .mark.br-row-wrap {
                border-bottom: 10px solid #343131;
                border-right: 10px solid #343131;
            }
        </style>
    @endif
</head>
<body>
<div class="wrapper">
    <div class="mark tl-row-wrap"></div>
    <div class="mark tr-row-wrap"></div>
    <div class="mark bl-row-wrap"></div>
    <div class="mark br-row-wrap"></div>
    @foreach (collect($data)->chunk(2) as $row)
        <div class="row-container">
            <div class="row">
                @foreach ($row as $index => $box)
                    <div class="box">
                        <img class="artwork" src="{{ $box['image'] }}" alt="Artwork">
                        <div class="dashed-line"></div>
                        <div class="right-block">
                            {!! $box['qrCode'] !!}
                            <div class="rotated-text-wrapper">
                                <div class="rotated-text">
                                    {{ $box['label'] }}<br>
                                    {{ $box['name'] }}
                                </div>
                            </div>
                        </div>

                        <div class="mark tl"></div>
                        <div class="mark tr"></div>
                        <div class="mark bl"></div>
                        <div class="mark br"></div>
                    </div>

                    @if ($index % 2 === 0)
                        <div class="gap"></div>
                    @endif
                @endforeach
            </div>
        </div>
    @endforeach
</div>
</body>
</html>
