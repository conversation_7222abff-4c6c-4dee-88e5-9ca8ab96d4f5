<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;

Route::get('/test-shipx', function () {
    $url = 'https://shipx-api.swiftpod.com/v1/shipments';
    $token = 'your_token_here'; // Thay bằng token thực
    
    $data = [
        'reference' => 'test-ref-1',
        'from_address' => [
            'name' => 'Test Sender',
            'street1' => '123 Test St',
            'city' => 'Test City',
            'state' => 'CA',
            'zip' => '94103',
            'country' => 'US',
            'phone' => '4151234567',
            'email' => '<EMAIL>'
        ],
        'to_address' => [
            'name' => 'Test Receiver',
            'street1' => '456 Test Ave',
            'city' => 'Test City',
            'state' => 'CA',
            'zip' => '94103',
            'country' => 'US',
            'phone' => '4151234567',
            'email' => '<EMAIL>'
        ],
        'parcel' => [
            'length' => 20.2,
            'width' => 10.9,
            'height' => 5,
            'weight' => 65.9
        ]
    ];
    
    $response = Http::timeout(60)->withHeaders([
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer ' . $token,
    ])->post($url, $data);
    
    return [
        'status' => $response->status(),
        'headers' => $response->headers(),
        'body' => $response->body()
    ];
});
