# GetRateShipXJob Unit Tests

## Mô tả
File test này kiểm tra toàn bộ chức năng của `GetRateShipXJob` class, bao gồm:

- ✅ Xử lý thành công khi API trả về rates
- ✅ Xử lý lỗi khi không tìm thấy shipment
- ✅ Xử lý lỗi khi không tìm thấy store setting
- ✅ Xử lý lỗi khi không tìm thấy sale order
- ✅ Xử lý lỗi khi API trả về status code khác 200
- ✅ Xử lý lỗi khi không có rates nào được trả về
- ✅ Xử lý lỗi khi service code không khớp với rates
- ✅ Test method `sendRequestShipX` riêng biệt
- ✅ Xử lý shipping quốc tế với customs info
- ✅ Xử lý timeout connection
- ✅ Sử dụng default dimensions khi shipment dimensions null
- ✅ Bao gồm tax identifiers khi có IOSS number

## Cách chạy test

### 1. Chạy tất cả tests trong file:
```bash
vendor/bin/pest tests/Feature/Job/GetRateShipXJobTest.php
```

### 2. Chạy test cụ thể:
```bash
vendor/bin/pest tests/Feature/Job/GetRateShipXJobTest.php --filter "job handles successful shipx rate request"
```

### 3. Chạy với verbose output:
```bash
vendor/bin/pest tests/Feature/Job/GetRateShipXJobTest.php --verbose
```

### 4. Sử dụng script helper:
```bash
php run-shipx-tests.php
```

## Cấu trúc Test

### Test Data Setup (beforeEach)
- Tạo Store, Warehouse, StoreSetting với API key
- Tạo SaleOrder và SaleOrderAddress
- Tạo Shipment với dimensions và service code
- Tạo Setting cho Google Chat error notification

### Mock Dependencies
- **Http::fake()**: Mock HTTP responses từ ShipX API
- **LabelRepository**: Mock các method dataAddressTo, dataAddressFrom, dataAddressReturn, dataCustomsInfoV2

### Test Cases

1. **Successful Rate Request**: Test khi API trả về rates thành công
2. **Shipment Not Found**: Test khi shipment ID không tồn tại
3. **Store Setting Not Found**: Test khi không tìm thấy API key
4. **Sale Order Not Found**: Test khi sale order bị xóa
5. **API Error Response**: Test khi API trả về error status
6. **No Rates Found**: Test khi API trả về empty rates array
7. **Service Code Mismatch**: Test khi service code không khớp
8. **sendRequestShipX Method**: Test riêng method HTTP request
9. **International Shipping**: Test với customs info cho shipping quốc tế
10. **Connection Timeout**: Test xử lý timeout
11. **Default Dimensions**: Test sử dụng default values
12. **Tax Identifiers**: Test bao gồm IOSS number

## Lưu ý quan trọng

### Dependencies cần mock:
- `LabelRepository` class và các methods của nó
- HTTP requests đến ShipX API
- Database models (Store, Shipment, SaleOrder, etc.)

### Assertions chính:
- Kiểm tra shipment được update với đúng rate
- Kiểm tra HTTP requests được gửi với đúng headers và data
- Kiểm tra exceptions được throw với đúng message
- Kiểm tra error handling không crash application

### Error Handling:
- Job sử dụng try-catch để handle tất cả exceptions
- Errors được gửi đến Google Chat thông qua `sendGoogleChat()` function
- Job không throw exceptions ra ngoài để tránh crash queue

## Troubleshooting

### Nếu test fail:
1. Kiểm tra database factories có đúng không
2. Kiểm tra mock LabelRepository methods
3. Kiểm tra HTTP fake responses
4. Kiểm tra exception messages có khớp với code thực tế không

### Common Issues:
- **Factory not found**: Cần tạo factories cho các models
- **Method not found**: Cần mock đúng methods của LabelRepository
- **Assertion failed**: Kiểm tra lại expected values vs actual values

## Cải tiến có thể thêm:
- Test với nhiều service codes khác nhau
- Test với các country codes khác nhau
- Test performance với large datasets
- Integration tests với real API (sandbox)
- Test retry logic nếu có
