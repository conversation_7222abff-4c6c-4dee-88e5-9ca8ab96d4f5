<?php

/**
 * <PERSON><PERSON>t to run GetRateShipXJob tests
 * Usage: php run-shipx-tests.php
 */

echo "Running GetRateShipXJob Tests...\n";
echo "================================\n\n";

// Run the specific test file
$command = 'vendor/bin/pest tests/Feature/Job/GetRateShipXJobTest.php --verbose';

echo "Executing: $command\n\n";

// Execute the command and capture output
$output = [];
$returnCode = 0;
exec($command . ' 2>&1', $output, $returnCode);

// Display output
foreach ($output as $line) {
    echo $line . "\n";
}

echo "\n================================\n";
echo "Test execution completed with return code: $returnCode\n";

if ($returnCode === 0) {
    echo "✅ All tests passed!\n";
} else {
    echo "❌ Some tests failed. Check output above.\n";
}
